---
description:
globs:
alwaysApply: false
---
# 新增素材扩充工具开发指南

本指南详细说明如何为素材扩充系统添加新的工具，以现有[InsightQATool](mdc:ls/deep_insight_qa_tool.py) 和 [SixDimensionsEvaluationTool](mdc:tools/enhance_tools/six_dimensions_evaluation_tool.py) 为参考。

## 开发步骤

### 1. 研究现有工具结构
首先查看现有工具的实现模式：
- 参考 [base_tool.py](mdc:tools/enhance_tools/base_tool.py) 了解基础架构
- 查看 [six_dimensions_evaluation_tool.py](mdc:tools/enhance_tools/six_dimensions_evaluation_tool.py) 等现有工具实现
- 了解工具分类、参数定义、适用场景的标准结构
- 注意错误处理和日志记录模式

### 2. 确定工具分类和定位
根据 `ToolCategory` 枚举选择合适的分类：

```python
class ToolCategory(Enum):
    CONTENT_ORGANIZATION = "A_内容结构化组织"     # timeline、表格、公式、框架图、思维导图等
    MULTIMODAL_PRESENTATION = "B_多模态呈现"     # 动态数据图、图片展示、emoji、插图等
    DEEP_INSIGHTS = "C_深度洞察"                # 对比、评估、insights洞察、深度提问和问答
    SMART_INTERACTION = "D_智能交互"            # 详细例子、模拟器、交互式媒体等
```

### 3. 创建新的工具文件
在 `tools/enhance_tools/` 目录下创建新文件，必须包含：

**文件头部和导入：**
```python
#!/usr/bin/env python3
"""
工具名称 - 工具分类
简洁描述工具的作用和效果
"""

import json
import os
import re
from typing import Any, Optional, Dict
from loguru import logger

from .base_tool import EnhancementTool, ToolCategory
```

**工具类定义要点：**
```python
class NewEnhanceTool(EnhancementTool):
    """工具名称 - 工具分类"""

    # 必需的类属性
    tool_name = "tool_identifier"  # 唯一标识符，用于配置和注册
    tool_description = "详细描述工具功能和价值"
    tool_category = ToolCategory.APPROPRIATE_CATEGORY

    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = ["内容类型1", "内容类型2", "内容类型3"]
    suitable_purposes = ["使用目标1", "使用目标2", "使用目标3"]
    required_conditions = ["必需条件1", "必需条件2", "必需条件3"]

    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = ["不适合的内容类型1", "不适合的内容类型2"]
    unsuitable_purposes = ["不适合的目标1", "不适合的目标2"]
    blocking_conditions = ["阻止条件1", "阻止条件2", "阻止条件3"]

    def __init__(self, config=None):
        self.config = config
        self.agent = None  # 如果需要使用Camel
        if config:
            self._init_model()  # 如果需要LLM支持

    def _init_model(self):
        """初始化Camel模型 - 适用于需要LLM数据提取的工具"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 定制化系统提示词
            system_prompt = """你是专业的数据提取专家，负责从内容中提取结构化信息。
请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "输出类型描述",
            "typical_output": "典型输出描述",
            "use_case": "详细的使用场景说明"
        }

    def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config and
            len(content) >= 600 and  # 根据工具需求调整最小长度
            self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, 'agent') and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filenf.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    exi)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_content(content, context)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """处理内容的核心逻辑 - 子类可以重写此方法"""
        # 如果不需要LLM，直接处理内容
        if not hasattr(self, 'agent') or self.agent is None:
            return self._process_without_llm(content, context)

        # 使用Camel进行数据提取
        return self._process_with_camel(content, context)

    def _process_without_llm(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """不使用LLM的内容处理逻辑"""
        # 实现基于规则的内容处理
        # 例如：文本分析、格式转换、数据统计等
        pass

    def _process_with_camel(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """使用Camel进行数据提取的处理逻辑"""
        purpose = context.get("purpose", "数据提取")

        # 构建结构化提示词
        prompt = f"""基于以下内容进行数据提取和分析：

**内容材料**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**处理目标**：{purpose}

**输出要求**：
请严格按照以下JSON格式输出：

```json
{{
    "summary": {{
        "content_theme": "内容主题概括",
        "target_audience": "{purpose}",
        "processing_focus": "处理重点说明"
    }},
    "extracted_data": {{
        // 根据具体工具需求定义数据结构
        "key1": "value1",
        "key2": "value2"
    }},
    "metadata": {{
        "processing_time": "处理时间",
        "data_quality": "数据质量评估"
    }}
}}
```"""

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0          # 提取JSONn_match = re.searc\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个响应
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            return result_data

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: Dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        intro = f"## 🔧 {self.tool_description}\n\n"

        # 添加工具输出的具体介绍
        # 根据工具的输出格式定制介绍内容

        return intro
```

### 4. Camel数据提取最佳实践

#### 4.1 何时使用Camel
**适用场景：**
- 需要从非结构化文本中提取结构化数据
- 需要智能分析和理解内容语义
- 需要生成评估、问答、洞察等复杂输出
- 内容处理需要上下文理解和推理

**不适用场景：**
- 简单的文本格式转换
- 基于规则的数据处理
- 纯计算或统计操作
- 不需要语义理解的操作

#### 4.2 Camel初始化模式
```python
def _init_model(self):
    """标准的Camel初始化模式"""
    try:
        from camel.agents import ChatAgent
        from camel.messages import BaseMessage
        from camel.models import ModelFactory
        from camel.types import ModelPlatformType

        # 使用配置中的模型设置
        model_config = self.config.get("model", {})
        self.model = ModelFactory.create(
            model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
            model_type=model_config.get("type", "openai/gpt-4o-mini"),
            api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
            url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
        )

        # 根据工具特点定制系统提示词
        system_prompt = self._get_system_prompt()

        self.agent = ChatAgent(
            system_message=BaseMessage.make_assistant_message("", system_prompt),
            model=self.model,
        )
        logger.info(f"{self.tool_name}模型初始化成功")
    except Exception as e:
        logger.warning(f"{self.tool_name}模型初始化失败: {e}")

def _get_system_prompt(self) -> str:
    """获取工具专用的系统提示词"""
    return """你是专业的数据提取专家，负责从内容中提取结构化信息。
请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""
```

#### 4.3 结构化数据提取模式
```python
def _extract_structured_data(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """结构化数据提取的标准模式"""

    # 1. 构建结构化提示词
    prompt = self._build_extraction_prompt(content, context)

    # 2. 调用Camel agent
    response = self.agent.step(prompt)
    response_content = response.msgs[0].content

    # 3. 提取和解析JSON
    return self._parse_json_response(response_content)

def _build_extraction_prompt(self, content: str, context: Dict[str, Any]) -> str:
    """构建数据提取提示词"""
    purpose = context.get("purpose", "数据提取")

    return f"""基于以下内容进行{self.tool_description}：

**内容材料**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**处理目标**：{purpose}

**输出要求**：
请严格按照以下JSON格式输出：

```json
{{
    // 根据具体工具需求定义JSON结构
    "summary": {{"theme": "主题", "focus": "重点"}},
    "data": {{"key": "value"}},
    "metadata": {{"quality": "质量评估"}}
}}
```"""

def _parse_json_response(self, response_content: str) -> Optional[Dict[str, Any]]:
    """解析JSON响应的标准方法"""
    try:
        # 尝试提取代码块中的JSON
        json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # 尝试直接解析整个响应
            json_str = response_content.strip()

        return json.loads(json_str)
    except Exception as e:
        logger.error(f"JSON解析失败: {e}")
        return None
```

### 5. 更新模块导入
修改 [__init__.py](mdc:tools/enhance_tools/__init__.py)：
- 添加新工具的import语句
- 在`__all__`列表中添加工具类名

```python
from .new_enhance_tool import NewEnhanceTool

__all__ = [
    'EnhancementTool',
    'ToolCategory',
    'ScreenRecordingTool',
    'TimelineTool',
    'SixDimensionsEvaluationTool',
    'DeepInsightQATool',
    'NewEnhanceTool'  # 添加新工具
]
```

### 6. 更新工具注册表
修改 [material_enhancement.py](mdc:agents/material_enhancement.py) 中的 `ToolRegistry`：

```python
class ToolRegistry:
    _tools = {
        "screen_recording": ScreenRecordingTool,
        "timeline_generation": TimelineTool,
        "six_dimensions_evaluation": SixDimensionsEvaluationTool,
        "deep_insight_qa": DeepInsightQATool,
        "new_tool_identifier": NewEnhanceTool,  # 添加新工具
    }
```

### 7. 更新MaterialEnhancer配置
在 `MaterialEnhancer._initialize_tools()` 方法中添加新工具的初始化逻辑：

```python
def _initialize_tools(self) -> List[EnhancementTool]:
    tools = []

    # 现有工具...

    if self.config.get("new_tool_identifier", True):  # 默认启用
        tools.append(NewEnhanceTool(self.config_dict))
        logger.info("✅ 已启用新工具")

    return tools
```

### 8. 更新配置文件
在 `config/config.yaml` 的 `material_enhance` 配置部分添加新工具的开关：

```yaml
# 素材扩充配置
material_enhance:
  screen_record: true
  timeline_generation: true
  six_dimensions_evaluation: true
  deep_insight_qa: true  # 深度洞察问答工具
  new_tool_identifier: true  # 新工具配置开关
```

**配置说明：**
- 配置项名称应与工具的 `tool_name` 保持一致
- 默认值建议设为 `true`，便于用户直接使用
- 添加注释说明工具的作用

### 9. 测试和验证
创建测试脚本验证新工具：

```python
#!/usr/bin/env python3
"""测试新工具"""

def test_new_tool():
    from tools.enhance_tools import NewEnhanceTool

    # 测试实例化
    tool = NewEnhanceTool()
    print(f"✅ {tool.tool_name} 实例化成功")

    # 测试工具信息
    info = tool.get_tool_info()
    print(f"工具信息: {info}")

    # 测试可用性检查
    can_apply = tool.can_apply("测试内容", "测试目标", {})
    print(f"可用性检查: {can_apply}")

if __name__ == "__main__":
    test_new_tool()
```

## 重要注意事项

### 🎨 设计原则
- **功能聚焦**：每个工具应该有明确的单一职责
- **智能适配**：通过适用场景描述帮助LLM做出正确选择
- **用户友好**：提供清晰的输出和错误提示
- **数据驱动**：对于复杂数据处理，优先使用Camel进行智能提取

### 🔧 技术要点
- **配置管理**：支持通过config.yaml配置启用/禁用
- **错误处理**：优雅处理异常情况，提供有意义的错误信息
- **日志记录**：使用loguru记录关键操作和状态
- **文件管理**：正确处理输出目录和文件路径
- **数据格式**：统一使用JSON格式保存结构化数据
- **Camel集成**：正确初始化和使用Camel进行数据提取

### 📝 实现规范
- **类型提示**：使用完整的类型注解
- **文档字符串**：为所有公共方法提供清晰的文档
- **命名规范**：使用描述性的变量和方法名
- **代码组织**：保持方法简洁，复杂逻辑拆分为私有方法
- **JSON处理**：使用标准的JSON提取和解析模式

### 🚨 常见问题
1. **依赖管理**：确保所有外部依赖都正确导入和处理
2. **配置检查**：在`can_apply`中进行必要的配置和依赖检查
3. **文件冲突**：正确处理已存在的输出文件
4. **内存管理**：对于大文件或复杂处理，注意内存使用
5. **Camel集成**：确保正确处理API调用和响应，处理网络异常
6. **JSON解析**：使用健壮的JSON提取和解析方法

### ✅ 验证清单
- [ ] 工具类正确继承`EnhancementTool`基类
- [ ] 所有必需的类属性都已定义
- [ ] 适用场景和不适用场景描述清晰准确
- [ ] `get_tool_info`方法返回完整信息
- [ ] `can_apply`方法实现合理的检查逻辑
- [ ] `apply_tool`方法处理所有必要的情况
- [ ] `generate_intro`方法生成有用的介绍内容
- [ ] 工具在`__init__.py`中正确导出
- [ ] 工具在`ToolRegistry`中正确注册
- [ ] 工具在`MaterialEnhancer`中正确初始化
- [ ] 工具配置开关已添加到`config/config.yaml`
- [ ] 如需要LLM，Camel模型正确初始化
- [ ] JSON数据提取和解析逻辑健壮
- [ ] 错误处理和日志记录完善
- [ ] 测试验证功能正常

## 示例参考
参考现有工具的完整实现：
- [TimelineTool](mdc:tools/enhance_tools/timeline_tool.py) - 展示内容分析和结构化输出
- [DeepInsightQATool](mdc:tools/enhance_tools/deep_insight_qa_tool.py) - 展示Camel集成和复杂数据提取
- [SixDimensionsEvaluationTool](mdc:tools/enhance_tools/six_dimensions_evaluation_tool.py) - 展示评估类工具和雷达图生成
- [ScreenRecordingTool](mdc:tools/enhance_tools/screen_recording_tool.py) - 展示外部工具集成和配置管理

这些示例展示了：
- 完整的工具类结构
- 优雅的错误处理机制
- 标准的配置和初始化模式
- 有效的LLM智能选择支持
- Camel数据提取的最佳实践
