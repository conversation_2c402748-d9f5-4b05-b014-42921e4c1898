# animate_markdown

## 效果

将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
支持各种Markdown元素，包括标题、列表、代码块、表格等。


## 使用场景

- 展示格式化的文本内容，如教程说明、演示文稿
- 在动画中展示结构化的信息，如列表和表格
- 显示带有语法高亮的代码片段
- 创建包含文本和图片的混合内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | Markdown格式的文本内容 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| animation_style | str | 内容显示的动画效果。可选值：fadeIn, create, write, fadeShift, succession, lagged | 否 | fadeIn |

## DSL示例

### 示例 1

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n    - 缩进 1\n    - 缩进 2\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
    "animation_style": "fadeIn",
    "narration": "这是一个Markdown示例，包含标题、文本和代码。"
  }
}
```

## 注意事项

- 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
- 根据内容会自动调整大小以适应场景
- 不同的animation_style提供不同的动画效果，可以根据内容类型选择合适的效果

