# animate_universal_display

## 效果

通用展示函数，支持文字、表格、图像等多种内容类型的动态展示。
使用Transform动画实现内容的平滑变化，包含位置、大小、形状的变换效果。


## 使用场景

- 展示多种不同类型的内容（文本、表格、图像）
- 需要动态变换展示内容的场景
- 制作内容丰富的教学视频
- 演示文档或报告的多媒体内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content_type | str | 内容类型，支持 'text', 'table', 'image', 'markdown' | 是 | - |
| content | str或dict | 要展示的内容。text类型为字符串；table类型为包含data和headers的字典；image类型为图片路径；markdown类型为markdown文本 | 是 | - |
| title | str | 显示在内容上方的标题 | 否 | None |
| narration | str | 播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| position | str | 内容在画面中的位置，支持 'center', 'left', 'right', 'top', 'bottom' | 否 | center |
| scale_factor | float | 内容的缩放因子 | 否 | 1.0 |
| transform_style | str | Transform动画的样式，支持 'fade', 'rotate', 'slide', 'morph' | 否 | fade |

## DSL示例

### 示例 1

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "text",
    "content": "这是一段重要的文本内容，用于演示文本展示功能。",
    "title": "文本展示示例",
    "narration": "现在我们来看一段文本内容的展示。",
    "position": "center",
    "scale_factor": 1.0,
    "transform_style": "fade"
  }
}
```

### 示例 2

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "table",
    "content": {
      "headers": [
        "姓名",
        "年龄",
        "职业"
      ],
      "data": [
        [
          "张三",
          "25",
          "工程师"
        ],
        [
          "李四",
          "30",
          "设计师"
        ],
        [
          "王五",
          "28",
          "产品经理"
        ]
      ]
    },
    "title": "员工信息表",
    "narration": "这是一个展示员工信息的表格。",
    "position": "center",
    "transform_style": "slide"
  }
}
```

### 示例 3

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "image",
    "content": "assets/example_image.png",
    "title": "图像展示",
    "narration": "接下来我们看一张图片。",
    "position": "center",
    "scale_factor": 0.8,
    "transform_style": "rotate"
  }
}
```

### 示例 4

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "markdown",
    "content": "## 功能特点\n- **高效**: 快速处理大量数据\n- **灵活**: 支持多种配置选项\n- **可靠**: 经过充分测试\n",
    "title": "产品特性",
    "narration": "让我们了解一下产品的主要特性。",
    "transform_style": "morph"
  }
}
```

## 注意事项

- Transform动画的playtime固定为0.5秒
- 支持多种内容类型的智能排版和缩放
- 可以设置不同的Transform样式来实现不同的视觉效果
- 表格类型的content需要包含headers和data两个字段
- 图像类型的content应为有效的图片文件路径
- 所有内容都会自动适应屏幕尺寸并合理排布

