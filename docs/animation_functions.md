# 动画函数文档

## 目录

- [animate_mindmap](#animate_mindmap)
- [animate_side_by_side_comparison](#animate_side_by_side_comparison)
- [animate_timeline](#animate_timeline)
- [animate_chart](#animate_chart)
- [animate_architecture_diagram](#animate_architecture_diagram)
- [animate_counter](#animate_counter)
- [animate_markdown](#animate_markdown)
- [animate_video](#animate_video)
- [animate_image](#animate_image)
- [animate_highlight_content](#animate_highlight_content)
- [animate_universal_display](#animate_universal_display)

---

# animate_architecture_diagram

## 效果

在Manim场景中直接播放一个架构图动画。该架构图是使用ExcalidrawToolkit根据文本描述生成的视频。
通常用于全屏展示复杂的系统架构或流程图。


## 使用场景

- 可视化软件架构
- 展示系统组件及其交互
- 解释数据流或业务流程
- 需要通过Excalidraw风格图表进行说明的场景

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content_description | str | 用于生成Excalidraw视频的文本描述，ExcalidrawToolkit将使用此描述来创建图表内容 | 是 | - |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个微服务架构图，包含前端应用、API网关和多个后端服务。\n前端应用连接到API网关，网关将请求路由到用户服务、产品服务和支付服务。\n这些服务分别连接到各自的数据库。\n",
    "narration": "这个架构图展示了我们的微服务系统结构，以及各组件之间的数据流。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_architecture_diagram",
  "params": {
    "content_description": "一个数据处理流程图，展示从数据收集到分析的完整过程。\n包含数据源、数据收集器、数据存储、处理引擎和可视化工具等组件。\n显示数据如何从原始数据转换为可行性见解。\n",
    "id": "data_pipeline",
    "narration": "这个数据处理流程展示了从原始数据到最终分析结果的完整路径。"
  }
}
```

## 注意事项

- ExcalidrawToolkit会根据提供的文本描述自动生成架构图视频
- 内容描述越详细，生成的架构图越准确
- 生成的视频会自动缩放以适应场景大小
- 此功能需要与外部ExcalidrawToolkit组件配合使用


---

# animate_chart

## 效果

创建并播放条形图、折线图或雷达图的动画，支持单个或多个数据集。


## 使用场景

- 可视化数据趋势和比较（折线图、条形图）
- 展示多个类别在不同指标上的表现（雷达图）
- 在视频演示中动态呈现统计数据
- 对比不同数据集之间的关系

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| chart_type | str | 图表类型。可选值：'bar'（条形图）, 'line'（折线图）, 'radar'（雷达图） | 是 | - |
| data | dict | list[dict] | 图表数据。单数据集为dict格式（如{"A":10,"B":20}），多数据集为dict列表 | 是 | - |
| narration | str | 在图表显示时播放的语音旁白文本 | 是 | - |
| title | str | 图表标题 | 否 | None |
| animation_style | str | 图表入场动画。可选值：'fadeIn', 'grow', 'draw', 'update' | 否 | fadeIn |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| dataset_names | list[str] | 多数据集时，用于图例的数据集名称列表 | 否 | None |
| x_label | str | x轴标签（条形图和折线图有效） | 否 | None |
| y_label | str | y轴标签（条形图和折线图有效） | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "bar",
    "data": {
      "苹果": 75,
      "香蕉": 120,
      "橙子": 90
    },
    "title": "水果销量",
    "narration": "这是本月水果销量的条形图。",
    "x_label": "水果",
    "y_label": "销量 (千克)"
  }
}
```

### 示例 2

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "line",
    "data": [
      {
        "第一季度": 50,
        "第二季度": 65,
        "第三季度": 80,
        "第四季度": 70
      },
      {
        "第一季度": 40,
        "第二季度": 50,
        "第三季度": 60,
        "第四季度": 90
      }
    ],
    "title": "产品A vs 产品B 季度销售额",
    "dataset_names": [
      "产品A",
      "产品B"
    ],
    "narration": "此折线图比较了产品A和产品B的季度销售额。",
    "x_label": "季度",
    "y_label": "销售额 (万元)"
  }
}
```

### 示例 3

```json
{
  "type": "animate_chart",
  "params": {
    "chart_type": "radar",
    "data": [
      {
        "性能": 8,
        "价格": 6,
        "外观": 9,
        "易用性": 7,
        "可靠性": 8
      },
      {
        "性能": 9,
        "价格": 4,
        "外观": 7,
        "易用性": 8,
        "可靠性": 9
      }
    ],
    "title": "产品对比",
    "dataset_names": [
      "产品X",
      "产品Y"
    ],
    "narration": "这个雷达图展示了两款产品在五个维度上的评分对比。"
  }
}
```

## 注意事项

- 对于条形图和折线图，数据键作为x轴标签，值作为y轴数据点
- 对于雷达图，数据键作为各个轴的标签，值作为该轴上的数据点
- 如果提供多个数据集，建议同时提供dataset_names以便在图例中显示
- update动画风格对条形图有特殊效果，会显示数值从0到目标值的变化过程


---

# animate_counter

## 效果

在Manim场景中创建并播放计数器动画，支持数字递增/递减计数器和曲线增长图两种类型。
可以自定义起始值、目标值、标签、单位、动画时长和结束特效。


## 使用场景

- 显示随时间变化的数值，如统计数据、得分、加载进度等
- 以曲线图形式展示增长或变化趋势，并在终点显示目标值
- 强调关键性能指标(KPI)的变化

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| target_value | float | 计数器动画的目标值 | 是 | - |
| start_value | float | 计数器的起始值。仅用于'counter'类型 | 否 | 0 |
| counter_type | str | 计数器类型。可选值：'counter'（数字计数器）, 'curve'（曲线增长图） | 否 | counter |
| label | str | 计数器的标签或标题文本 | 否 | None |
| unit | str | 计数器的单位文本。对于'counter'类型，单位显示在数字之后；对于'curve'类型，单位与目标值一起显示在曲线末端 | 否 | None |
| duration | float | 计数器动画的持续时间（秒） | 否 | 2.0 |
| effect | str | 动画结束时应用的额外视觉效果。可选值：'flash', 'zoom' | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "counter",
    "target_value": 100,
    "label": "进度",
    "unit": "%",
    "duration": 3,
    "effect": "flash",
    "narration": "加载进度达到100%。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_counter",
  "params": {
    "counter_type": "curve",
    "target_value": 500,
    "label": "用户增长曲线",
    "unit": "用户",
    "duration": 4,
    "narration": "用户数量快速增长至500。"
  }
}
```

## 注意事项

- counter类型显示一个从起始值到目标值的数字动画
- curve类型显示一条增长曲线，终点显示目标值
- 结束特效在动画完成后应用，可增强视觉效果


---

# animate_highlight_content

## 效果

按顺序高亮一系列元素，或者对代码对象高亮特定行。


## 使用场景

- 逐步引导观众注意场景中的特定对象
- 逐行解释代码片段，高亮当前讨论的行
- 强调流程图或架构图中的特定组件序列

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| elements | list[str] | 要高亮的元素ID列表。如果指定lines参数，则此列表应只包含一个Code对象的ID | 是 | - |
| highlight_type | str | 高亮效果类型。可选值：'flash'（闪烁）, 'box'（边框）, 'underline'（下划线）, 'color'（颜色变化） | 否 | box |
| color | str | 高亮效果的颜色（十六进制或颜色名称） | 否 | #FFFF00 |
| duration_per_item | float | 每个元素或代码行组的高亮持续时间（秒） | 否 | 1.0 |
| lines | str | 要高亮的代码行范围，格式如"1-3,5,7-10"。如果提供此参数，elements应只有一个Code对象ID | 否 | None |
| narration | str | 播放动画时同步播放的语音旁白文本 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "text_obj1",
      "shape_obj2"
    ],
    "highlight_type": "flash",
    "color": "RED",
    "duration_per_item": 0.5,
    "narration": "首先看左边的文本，然后看右边的形状。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_highlight_content",
  "params": {
    "elements": [
      "code_block"
    ],
    "lines": "1-2,3",
    "highlight_type": "box",
    "color": "GREEN",
    "duration_per_item": 1.5,
    "narration": "现在我们来看这段代码。首先是函数定义，然后是注释。"
  }
}
```

## 注意事项

- 被高亮的元素必须已经在场景中存在并且有指定的ID
- 对于代码高亮，lines参数优先于highlight_type
- 高亮效果是暂时的，结束后元素会恢复原始状态


---

# animate_image

## 效果

在Manim场景中显示图像，支持缩放平移动画和注释文本。


## 使用场景

- 展示需要详细解释的图片或截图
- 展示图表、图解或可视化内容
- 显示产品或界面截图并添加注释说明

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| image_path | str | 要显示的图片的本地文件路径 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在图片显示时播放的语音旁白文本 | 是 | - |
| annotation | str或List[str] | 作为注释显示在图片旁边的文本，支持Markdown格式 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_image",
  "params": {
    "image_path": "assets/manim_logo.png",
    "narration": "让我们看看这张图片。",
    "annotation": "这是一张示例图片，展示了重要的内容。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_image",
  "params": {
    "image_path": "assets/manim_logo.png",
    "id": "architecture_diagram",
    "annotation": "## 系统架构\n- 前端组件\n- 后端服务\n- 数据存储\n",
    "narration": "这张架构图展示了系统的主要组件和它们之间的关系。"
  }
}
```

## 注意事项

- 图片文件必须存在且路径正确，否则会抛出FileNotFoundError
- 图片会自动缩放以适应场景，并根据图片比例可能会添加平移动画
- 如果提供annotation，它会作为文本显示在图片右侧


---

# animate_markdown

## 效果

将Markdown格式的文本内容渲染为富文本并在Manim场景中播放动画。
支持各种Markdown元素，包括标题、列表、代码块、表格等。


## 使用场景

- 展示格式化的文本内容，如教程说明、演示文稿
- 在动画中展示结构化的信息，如列表和表格
- 显示带有语法高亮的代码片段
- 创建包含文本和图片的混合内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content | str | Markdown格式的文本内容 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| animation_style | str | 内容显示的动画效果。可选值：fadeIn, create, write, fadeShift, succession, lagged | 否 | fadeIn |

## DSL示例

### 示例 1

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
    "animation_style": "fadeIn",
    "narration": "这是一个Markdown示例，包含标题、文本和代码。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_markdown",
  "params": {
    "content": "## 数据比较📊\n\n| 产品 | 价格 | 评分 |\n| ---- | ---- | ---- |\n| A产品 | ¥199 | 4.5分 |\n| B产品 | ¥299 | 4.8分 |\n",
    "animation_style": "lagged",
    "narration": "这个表格比较了两款产品的价格和评分。"
  }
}
```

## 注意事项

- 支持大多数标准Markdown语法，包括标题、列表、代码块、表格等
- 根据内容会自动调整大小以适应场景
- 不同的animation_style提供不同的动画效果，可以根据内容类型选择合适的效果


---

# animate_mindmap

## 效果

创建交互式思维导图，支持节点聚焦、子树展开和动态布局调整。


## 使用场景

- 展示知识结构和概念层次关系
- 教学中的概念图解和思路梳理
- 项目规划和任务分解可视化
- 复杂信息的结构化展示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| data_source | str | dict | 思维导图数据源，可以是JSON文件路径或字典数据. 如果复用之前已创建的mindmap（需要提供id），可以不设置 data_source | 否 | None |
| layout_style | str | 布局样式。可选值：'balance'（平衡布局）, 'left_to_right'（从左到右）, 'right_to_left'（从右到左） | 否 | balance |
| max_depth | int | 显示的最大层级深度 | 否 | 3 |
| focus_sequence | list[str] | 按顺序聚焦的节点文本列表，用于引导观众注意力 | 否 | None |
| narration | str | 在思维导图显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_mindmap",
  "params": {
    "data_source": "{\n  \"标题\": \"人工智能\",\n  \"子章节\": [\n    {\n      \"标题\": \"机器学习\",\n      \"子章节\": [\n        {\"标题\": \"监督学习\"},\n        {\"标题\": \"无监督学习\"},\n        {\"标题\": \"强化学习\"}\n      ]\n    },\n    {\n      \"标题\": \"深度学习\",\n      \"子章节\": [\n        {\"标题\": \"神经网络\"},\n        {\"标题\": \"卷积网络\"},\n        {\"标题\": \"循环网络\"}\n      ]\n    }\n  ]\n}\n",
    "layout_style": "balance",
    "max_depth": 3,
    "id": "AI_mindmap",
    "focus_sequence": [
      "人工智能",
      "机器学习",
      "深度学习"
    ],
    "narration": "让我们通过这个思维导图来了解人工智能的主要分支和技术体系。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_mindmap",
  "params": {
    "id": "AI_mindmap",
    "focus_sequence": [
      "机器学习",
      "监督学习",
      "无监督学习"
    ],
    "narration": "让我们更深入地了解一下机器学习的两个主要分支，监督学习和无监督学习。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_mindmap",
  "params": {
    "data_source": "assets/mindmap_data.json",
    "layout_style": "left_to_right",
    "max_depth": 2,
    "narration": "这是一个从文件加载的思维导图结构。"
  }
}
```

## 注意事项

- 数据源支持JSON文件路径或直接传入字典格式
- focus_sequence参数可以创建引导式的节点聚焦动画
- 思维导图会自动调整布局以适应屏幕尺寸
- 支持中文文本和自动换行


---

# animate_side_by_side_comparison

## 效果

创建左右两栏并排比较的布局，可以比较不同类型的内容（文本、代码、图像等）。


## 使用场景

- 对比两种不同的代码实现或算法
- 比较“之前”与“之后”的状态
- 并列展示问题和解决方案
- 对比两个图像、图表或设计

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| left_content | str | 左侧内容（文本、代码字符串或图像路径） | 是 | - |
| left_type | str | 左侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| right_content | str | 右侧内容（文本、代码字符串或图像路径） | 是 | - |
| right_type | str | 右侧内容类型。可选值：'text', 'code', 'json', 'image', 'markdown' | 是 | - |
| left_title | str | 左侧窗格的标题 | 否 | None |
| right_title | str | 右侧窗格的标题 | 否 | None |
| transition | str | 内容入场的动画效果。可选值：'fadeIn', 'slideUp', 'none' | 否 | fadeIn |
| narration | str | 在内容显示时播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "left_content": "def fib_recursive(n):\n    if n <= 1:\n        return n\n    return fib_recursive(n-1) + fib_recursive(n-2)\n",
    "left_type": "code",
    "left_title": "递归斐波那契",
    "right_content": "def fib_iterative(n):\n    a, b = 0, 1\n    for _ in range(n):\n        a, b = b, a + b\n    return a\n",
    "right_type": "code",
    "right_title": "迭代斐波那契",
    "narration": "让我们比较斐波那契数列的递归和迭代实现。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "left_content": "{\n    \"name\": \"Python\",\n    \"year\": 1991,\n    \"creator\": \"Guido van Rossum\",\n    \"paradigms\": [\"面向对象\", \"命令式\", \"函数式\"]\n}\n",
    "left_type": "json",
    "left_title": "Python 信息",
    "right_content": "{\n    \"name\": \"Java\",\n    \"year\": 1995,\n    \"creator\": \"James Gosling\",\n    \"paradigms\": [\"面向对象\", \"命令式\"]\n}\n",
    "right_type": "json",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

### 示例 3

```json
{
  "type": "animate_side_by_side_comparison",
  "params": {
    "left_content": "# Python\n- 创建于1991年\n- 由Guido van Rossum开发\n- 支持面向对象、命令式和函数式编程\n",
    "left_type": "markdown",
    "left_title": "Python 信息",
    "right_content": "# Java\n- 创建于1995年\n- 由James Gosling开发\n- 主要支持面向对象编程\n",
    "right_type": "markdown",
    "right_title": "Java 信息",
    "transition": "fadeIn",
    "narration": "Python 和 Java 的信息对比。"
  }
}
```

## 注意事项

- 对于'image'类型，content应为图像文件的本地路径
- 对于'code'和'json'类型，会自动应用语法高亮
- 内容会自动缩放以适应各自的窗格大小
- 调用此函数会清除屏幕上的其他内容


---

# animate_timeline

## 效果

在Manim场景中创建并播放一个动态的、分段构建的时间轴动画。
每个事件包含时间点（年份）、标题、描述文本，并可选择性地包含图片和自定义颜色。
事件详情会交替显示在时间轴的上方和下方。


## 使用场景

- 展示项目里程碑或历史事件序列，具有更强的视觉吸引力
- 解释一个过程的各个阶段，每个阶段有清晰的标题和描述
- 可视化产品发布路线图，突出显示关键节点

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| events | list[EventData | dict[str, Any]] | 时间轴事件列表。每个元素可以是EventData对象或字典，包含year(时间点), title(标题), description(描述), emoji(表情), color(颜色), narration(事件旁白)等属性，其中narration会在当前时间轴事件显示的同时作为旁白播放。如果有全局的content_narration，那么事件的narration将被忽略。 | 是 | - |
| intro_narration | str | 时间轴动画开始时播放的语音旁白文本（会配合开始标题的显示动画一起播放） | 否 | None |
| outro_narration | str | 时间轴动画结束时播放的语音旁白文本（会配合最后整个时间轴缩放动画一起播放） | 否 | None |
| content_narration | str | 时间轴动画播放时同步播放的语音旁白文本（与每个事件的narration互斥） | 是 | - |
| title | str | 时间轴标题 | 否 | None |
| subtitle | str | 时间轴副标题 | 否 | None |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_timeline",
  "params": {
    "events": [
      {
        "year": "1950",
        "title": "The Beginning",
        "description": "A new era starts.",
        "emoji": "🌍",
        "color": "#F0E68C",
        "narration": "A new era starts."
      },
      {
        "year": "1965",
        "title": "Major Discovery",
        "description": "Key findings published.",
        "emoji": "🌞",
        "color": "#FFA500",
        "narration": "Key findings published."
      },
      {
        "year": "1980",
        "title": "Expansion",
        "description": "Growth and development.",
        "emoji": "🌛",
        "color": "#B22222",
        "narration": "Growth and development."
      },
      {
        "year": "2000",
        "title": "New Century",
        "description": "Looking ahead.",
        "emoji": "🪵",
        "color": "#FFC0CB",
        "narration": "Looking ahead."
      },
      {
        "year": "2020",
        "title": "Modern Times",
        "description": "Current state of affairs.",
        "emoji": "🚀",
        "color": "#9370DB",
        "narration": "Current state of affairs."
      }
    ],
    "title": "Historical Timeline",
    "intro_narration": "A journey through time, highlighting key moments.",
    "outro_narration": "This is the end of the timeline."
  }
}
```

## 注意事项

- 事件列表按数组中的顺序呈现，而非按年份排序
- 时间轴展示时会先显示主轴，然后顺序显示每个事件
- 可以为每个事件指定颜色，或使用默认颜色方案
- emoji属性会尝试下载并显示对应图标，如果失败则仅显示简单节点
- 如果时间轴节点较多（超过5个），每个节点单独的narration会使视频变得冗长，可以设置简洁的整体content_narration来介绍时间轴的背景信息


---

# animate_universal_display

## 效果

通用展示函数，支持文字、表格、图像等多种内容类型的动态展示。
使用Transform动画实现内容的平滑变化，包含位置、大小、形状的变换效果。


## 使用场景

- 展示多种不同类型的内容（文本、表格、图像）
- 需要动态变换展示内容的场景
- 制作内容丰富的教学视频
- 演示文档或报告的多媒体内容

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| content_type | str | 内容类型，支持 'text', 'table', 'image', 'markdown' | 是 | - |
| content | str或dict | 要展示的内容。text类型为字符串；table类型为包含data和headers的字典；image类型为图片路径；markdown类型为markdown文本 | 是 | - |
| title | str | 显示在内容上方的标题 | 否 | None |
| narration | str | 播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |
| position | str | 内容在画面中的位置，支持 'center', 'left', 'right', 'top', 'bottom' | 否 | center |
| scale_factor | float | 内容的缩放因子 | 否 | 1.0 |
| transform_style | str | Transform动画的样式，支持 'fade', 'rotate', 'slide', 'morph' | 否 | fade |

## DSL示例

### 示例 1

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "text",
    "content": "这是一段重要的文本内容，用于演示文本展示功能。",
    "title": "文本展示示例",
    "narration": "现在我们来看一段文本内容的展示。",
    "position": "center",
    "scale_factor": 1.0,
    "transform_style": "fade"
  }
}
```

### 示例 2

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "table",
    "content": {
      "headers": [
        "姓名",
        "年龄",
        "职业"
      ],
      "data": [
        [
          "张三",
          "25",
          "工程师"
        ],
        [
          "李四",
          "30",
          "设计师"
        ],
        [
          "王五",
          "28",
          "产品经理"
        ]
      ]
    },
    "title": "员工信息表",
    "narration": "这是一个展示员工信息的表格。",
    "position": "center",
    "transform_style": "slide"
  }
}
```

### 示例 3

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "image",
    "content": "assets/example_image.png",
    "title": "图像展示",
    "narration": "接下来我们看一张图片。",
    "position": "center",
    "scale_factor": 0.8,
    "transform_style": "rotate"
  }
}
```

### 示例 4

```json
{
  "type": "animate_universal_display",
  "params": {
    "content_type": "markdown",
    "content": "## 功能特点\n- **高效**: 快速处理大量数据\n- **灵活**: 支持多种配置选项\n- **可靠**: 经过充分测试\n",
    "title": "产品特性",
    "narration": "让我们了解一下产品的主要特性。",
    "transform_style": "morph"
  }
}
```

## 注意事项

- Transform动画的playtime固定为0.5秒
- 支持多种内容类型的智能排版和缩放
- 可以设置不同的Transform样式来实现不同的视觉效果
- 表格类型的content需要包含headers和data两个字段
- 图像类型的content应为有效的图片文件路径
- 所有内容都会自动适应屏幕尺寸并合理排布


---

# animate_video

## 效果

在Manim场景中播放视频文件，支持可选的文本叠加和语音旁白。


## 使用场景

- 在教程或演示中展示屏幕录制或外部视频片段
- 播放动画片段作为更复杂场景的一部分
- 叠加解释性文本或字幕到视频内容上
- 配合旁白同步视频演示

## 参数

| 参数名 | 类型 | 描述 | 是否必需 | 默认值 |
| ------ | ---- | ---- | -------- | ------ |
| scene | FeynmanScene | Manim场景实例（由系统自动传入） | 否 | - |
| video_path | str | 要播放的视频文件的本地路径 | 是 | - |
| overlay_text | str | 可选的叠加文本，显示在视频之上。多行文本用"\n"分隔 | 否 | None |
| overlay_animation_delay | float | 每行文本动画之间的延迟（秒） | 否 | 1.0 |
| narration | str | 在视频播放时同步播放的语音旁白文本 | 是 | - |
| id | str | 创建的Manim Mobject的唯一标识符 | 否 | None |

## DSL示例

### 示例 1

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "narration": "这是一个演示视频。"
  }
}
```

### 示例 2

```json
{
  "type": "animate_video",
  "params": {
    "video_path": "assets/demo.mp4",
    "overlay_text": "重要提示！\n请注意这个关键点",
    "overlay_animation_delay": 0.5,
    "narration": "请注意视频中的这个重点部分。"
  }
}
```

## 注意事项

- 视频文件必须存在且路径正确，否则函数会记录错误并返回
- 视频会自动缩放以适应场景
- 叠加文本会依次显示，每行之间有指定的延迟


---
