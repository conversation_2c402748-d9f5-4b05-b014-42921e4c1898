#!/usr/bin/env python3
"""
测试架构图生成工具
"""

import os
import tempfile
from tools.enhance_tools import ArchitectureDiagramTool


def test_architecture_diagram_tool():
    """测试架构图生成工具的基本功能"""
    print("🧪 开始测试架构图生成工具")
    
    # 创建工具实例
    config = {
        "material": {
            "material_enhance": {
                "architecture_diagram": True
            }
        }
    }
    
    tool = ArchitectureDiagramTool(config)
    print(f"✅ {tool.tool_name} 实例化成功")
    
    # 测试工具信息
    info = tool.get_tool_info()
    print(f"📋 工具信息:")
    print(f"  - 名称: {info['name']}")
    print(f"  - 描述: {info['description']}")
    print(f"  - 分类: {info['category']}")
    print(f"  - 适用内容: {info['suitable_content']}")
    print(f"  - 适用目标: {info['suitable_purposes']}")
    
    # 测试内容
    test_content = """
    这是一个微服务架构系统的设计方案。整个系统包含以下主要组件：
    
    前端应用：用户界面，负责与用户交互
    API网关：统一入口，负责路由和认证
    用户服务：处理用户相关的业务逻辑
    产品服务：管理产品信息和库存
    支付服务：处理支付和订单
    数据库：分别为每个服务提供数据存储
    
    数据流程：
    1. 用户通过前端应用发起请求
    2. 请求经过API网关进行认证和路由
    3. 网关将请求转发到相应的微服务
    4. 微服务处理业务逻辑并访问数据库
    5. 处理结果通过网关返回给前端
    
    这种架构设计具有良好的可扩展性和维护性。
    """
    
    # 测试可用性检查
    can_apply = tool.can_apply(test_content, "架构可视化", {})
    print(f"🔍 可用性检查: {can_apply}")
    
    if can_apply:
        # 创建临时输出目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 使用临时目录: {temp_dir}")
            
            # 测试工具应用
            context = {"purpose": "微服务架构展示"}
            result = tool.apply_tool(test_content, temp_dir, context)
            
            if result:
                print(f"✅ 工具执行成功")
                print(f"  - 工具名称: {result['tool_name']}")
                print(f"  - 状态: {result['status']}")
                print(f"  - 文件路径: {result['file_path']}")
                
                # 测试介绍生成
                intro = tool.generate_intro(result)
                print(f"📝 生成的介绍:")
                print(intro[:500] + "..." if len(intro) > 500 else intro)
                
                # 显示生成的配置数据
                data = result.get('data', {})
                if data:
                    architecture_config = data.get('architecture_config', {})
                    print(f"🏗️ 架构配置:")
                    print(f"  - 描述长度: {len(architecture_config.get('content_description', ''))}")
                    print(f"  - 动画类型: {architecture_config.get('animation_type', '')}")
                    print(f"  - 图表ID: {architecture_config.get('id', '')}")
                    print(f"  - 旁白: {architecture_config.get('narration', '')}")
                
            else:
                print("❌ 工具执行失败")
    else:
        print("⚠️ 工具不适用于当前内容")
    
    print("🧪 测试完成")


if __name__ == "__main__":
    test_architecture_diagram_tool()
