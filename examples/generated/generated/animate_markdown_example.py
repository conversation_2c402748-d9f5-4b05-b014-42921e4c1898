# Generated by <PERSON><PERSON>man DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_markdown_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_markdown
        animate_markdown(
            scene=self,
            content="# 主标🚗题\n\n⚠️ 这是一段普通文本，支持**粗体**和*斜体*。\n\n> 🚰 这是一个引用💦块。\n\n## 子标题\n\n- 列表🐆 项1\n- 🐇 列表项2\n- $E = mc^2$\n    - 缩进 1\n    - 缩进 2\n\n$$\na^2 = b^2 + c^2\n$$\n\n```python\ndef hello_world():\n    print(\"Hello, world!\")\n```\n",
            animation_style="fadeIn",
            narration="这是一个Markdown示例，包含标题、文本和代码。"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
