# Generated by <PERSON>ynman DSL v2 Code Generator
# Author: 自动生成
# -*- coding: utf-8 -*-

import sys
import os
root_dir = os.getcwd()
sys.path.append(root_dir)

from manim import *
from dsl.v2.core.scene import FeynmanScene
from dsl.v2.animation_functions import *  # IMPORTANT: Ensures all animations are registered

class animate_universal_display_(FeynmanScene):
    config.background_color = '#FFFFFF'

    def construct(self):
        self.add_background()

        # Action 1: animate_universal_display
        animate_universal_display(
            scene=self,
            content_type="text",
            content="这是一段重要的文本内容，用于演示文本展示功能。",
            title="文本展示示例",
            narration="现在我们来看一段文本内容的展示。",
            position="center",
            scale_factor=1.0,
            transform_style="fade"
        )

        # Action 2: animate_universal_display
        animate_universal_display(
            scene=self,
            content_type="table",
            content={'headers': ['姓名', '年龄', '职业'], 'data': [['张三', '25', '工程师'], ['李四', '30', '设计师'], ['王五', '28', '产品经理']]},
            title="员工信息表",
            narration="这是一个展示员工信息的表格。",
            position="center",
            transform_style="slide"
        )

        # Action 3: animate_universal_display
        animate_universal_display(
            scene=self,
            content_type="image",
            content="/Users/<USER>/Documents/git/agentic-feynman/assets/manim_logo.png",
            title="图像展示",
            narration="接下来我们看一张图片。",
            position="center",
            scale_factor=0.8,
            transform_style="rotate"
        )

        # Action 4: animate_universal_display
        animate_universal_display(
            scene=self,
            content_type="markdown",
            content="## 功能特点\n- **高效**: 快速处理大量数据\n- **灵活**: 支持多种配置选项\n- **可靠**: 经过充分测试\n",
            title="产品特性",
            narration="让我们了解一下产品的主要特性。",
            transform_style="morph"
        )
        # --- Final wait to hold the last frame ---
        self.wait(1)
