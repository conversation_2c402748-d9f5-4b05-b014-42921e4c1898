"""
effect: |
    通用展示函数，支持文字、表格、图像等多种内容类型的动态展示。
    使用Transform动画实现内容的平滑变化，包含位置、大小、形状的变换效果。

use_cases:
    - 展示多种不同类型的内容（文本、表格、图像）
    - 需要动态变换展示内容的场景
    - 制作内容丰富的教学视频
    - 演示文档或报告的多媒体内容

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  content_type:
    type: str
    desc: 内容类型，支持 'text', 'table', 'image', 'markdown'
    required: true
  content:
    type: str或dict
    desc: 要展示的内容。text类型为字符串；table类型为包含data和headers的字典；image类型为图片路径；markdown类型为markdown文本
    required: true
  title:
    type: str
    desc: 显示在内容上方的标题
    default: None
  narration:
    type: str
    desc: 播放的语音旁白文本
    required: true
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None
  position:
    type: str
    desc: 内容在画面中的位置，支持 'center', 'left', 'right', 'top', 'bottom'
    default: 'center'
  scale_factor:
    type: float
    desc: 内容的缩放因子
    default: 1.0
  transform_style:
    type: str
    desc: Transform动画的样式，支持 'fade', 'rotate', 'slide', 'morph'
    default: 'fade'

dsl_examples:
    - type: animate_universal_display
      params:
        content_type: text
        content: "这是一段重要的文本内容，用于演示文本展示功能。"
        title: "文本展示示例"
        narration: "现在我们来看一段文本内容的展示。"
        position: center
        scale_factor: 1.0
        transform_style: fade
    - type: animate_universal_display
      params:
        content_type: table
        content:
          headers: ["姓名", "年龄", "职业"]
          data:
            - ["张三", "25", "工程师"]
            - ["李四", "30", "设计师"]
            - ["王五", "28", "产品经理"]
        title: "员工信息表"
        narration: "这是一个展示员工信息的表格。"
        position: center
        transform_style: slide
    - type: animate_universal_display
      params:
        content_type: image
        content: "assets/example_image.png"
        title: "图像展示"
        narration: "接下来我们看一张图片。"
        position: center
        scale_factor: 0.8
        transform_style: rotate
    - type: animate_universal_display
      params:
        content_type: markdown
        content: |
          ## 功能特点
          - **高效**: 快速处理大量数据
          - **灵活**: 支持多种配置选项
          - **可靠**: 经过充分测试
        title: "产品特性"
        narration: "让我们了解一下产品的主要特性。"
        transform_style: morph

notes:
    - Transform动画的playtime固定为0.5秒
    - 支持多种内容类型的智能排版和缩放
    - 可以设置不同的Transform样式来实现不同的视觉效果
    - 表格类型的content需要包含headers和data两个字段
    - 图像类型的content应为有效的图片文件路径
    - 所有内容都会自动适应屏幕尺寸并合理排布
"""

import os
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union, cast

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene

import numpy as np
from loguru import logger
from manim import *

from dsl.v2.animation_functions.animate_markdown import _create_markdown_mobjects
from dsl.v2.themes.theme_utils import ThemeUtils
from utils.md_to_pango import MarkdownToSimplifiedConverter


def _create_text_content(text: str, title: Optional[str] = None) -> VGroup:
    """创建文本内容的Mobject"""
    content_group = VGroup()
    
    if title:
        title_text = Text(
            title, 
            font_size=52, 
            color=DARK_BLUE, 
            weight=BOLD,
            font="Arial"
        )
        content_group.add(title_text)
    
    # 处理长文本，自动换行
    max_width = 12  # 增加最大宽度
    words = text.split()
    lines = []
    current_line = []
    current_width = 0
    
    for word in words:
        word_width = len(word) * 0.25  # 调整字符宽度估算
        if current_width + word_width > max_width and current_line:
            lines.append(" ".join(current_line))
            current_line = [word]
            current_width = word_width
        else:
            current_line.append(word)
            current_width += word_width
    
    if current_line:
        lines.append(" ".join(current_line))
    
    for line in lines:
        line_text = Text(
            line, 
            font_size=38, 
            color=DARK_GRAY,
            font="Arial",
            line_spacing=1.2
        )
        content_group.add(line_text)
    
    # 垂直排列
    content_group.arrange(DOWN, aligned_edge=LEFT, buff=0.4)
    return content_group


def _create_table_content(table_data: Dict, title: Optional[str] = None) -> VGroup:
    """创建表格内容的Mobject"""
    content_group = VGroup()
    
    if title:
        title_text = Text(
            title, 
            font_size=52, 
            color=DARK_BLUE, 
            weight=BOLD,
            font="Arial"
        )
        content_group.add(title_text)
    
    headers = table_data.get("headers", [])
    data = table_data.get("data", [])
    
    if not headers or not data:
        error_text = Text(
            "表格数据格式错误", 
            font_size=38, 
            color=RED,
            font="Arial"
        )
        content_group.add(error_text)
        return content_group
    
    # 创建表格
    table_group = VGroup()
    
    # 创建表头
    header_row = VGroup()
    for header in headers:
        cell = Rectangle(
            width=3.0, 
            height=1.0, 
            color=BLUE, 
            fill_opacity=0.2, 
            stroke_color=BLUE,
            stroke_width=2
        )
        text = Text(
            str(header), 
            font_size=26, 
            color=DARK_BLUE,
            weight=BOLD,
            font="Arial"
        )
        cell_group = VGroup(cell, text)
        header_row.add(cell_group)
    header_row.arrange(RIGHT, buff=0.1)
    table_group.add(header_row)
    
    # 创建数据行
    for i, row in enumerate(data):
        data_row = VGroup()
        row_color = LIGHT_GRAY if i % 2 == 0 else WHITE
        for cell_data in row:
            cell = Rectangle(
                width=3.0, 
                height=0.8, 
                color=row_color, 
                fill_opacity=0.1, 
                stroke_color=GRAY,
                stroke_width=1
            )
            text = Text(
                str(cell_data), 
                font_size=22, 
                color=BLACK,
                font="Arial"
            )
            cell_group = VGroup(cell, text)
            data_row.add(cell_group)
        data_row.arrange(RIGHT, buff=0.1)
        table_group.add(data_row)
    
    # 垂直排列表格行
    table_group.arrange(DOWN, buff=0.05)
    content_group.add(table_group)
    
    # 垂直排列标题和表格
    content_group.arrange(DOWN, buff=0.6)
    return content_group


def _create_image_content(image_path: str, title: Optional[str] = None) -> VGroup:
    """创建图像内容的Mobject"""
    content_group = VGroup()
    
    if title:
        title_text = Text(
            title, 
            font_size=52, 
            color=DARK_BLUE, 
            weight=BOLD,
            font="Arial"
        )
        content_group.add(title_text)
    
    try:
        if not os.path.exists(image_path):
            error_text = Text(
                f"图片文件未找到: {image_path}", 
                font_size=38, 
                color=RED,
                font="Arial"
            )
            content_group.add(error_text)
            return content_group
        
        if image_path.endswith(".svg"):
            image = SVGMobject(image_path)
        else:
            image = ImageMobject(image_path)
        
        # 限制图像大小
        max_width = 10
        max_height = 7
        if image.width > max_width:
            image.scale(max_width / image.width)
        if image.height > max_height:
            image.scale(max_height / image.height)
        
        # 添加图像边框
        image_rect = SurroundingRectangle(
            image, 
            color=GRAY, 
            buff=0.1,
            stroke_width=2
        )
        image_group = VGroup(image_rect, image)
        content_group.add(image_group)
        
    except Exception as e:
        logger.error(f"创建图像时发生错误: {e}")
        error_text = Text(
            "图像加载失败", 
            font_size=38, 
            color=RED,
            font="Arial"
        )
        content_group.add(error_text)
    
    # 垂直排列
    content_group.arrange(DOWN, buff=0.6)
    return content_group


def _create_markdown_content(markdown_text: str, title: Optional[str] = None) -> VGroup:
    """创建Markdown内容的Mobject"""
    content_group = VGroup()
    
    if title:
        title_text = Text(
            title, 
            font_size=52, 
            color=DARK_BLUE, 
            weight=BOLD,
            font="Arial"
        )
        content_group.add(title_text)
    
    try:
        # 使用现有的markdown转换功能
        converter = MarkdownToSimplifiedConverter()
        simplified_markdown = converter.convert(markdown_text)
        
        # 创建markdown对象
        markdown_mobjects = _create_markdown_mobjects(simplified_markdown)
        if markdown_mobjects:
            for mobject in markdown_mobjects:
                content_group.add(mobject)
        else:
            # 如果markdown转换失败，使用简单文本
            text = Text(
                markdown_text, 
                font_size=38, 
                color=DARK_GRAY,
                font="Arial",
                line_spacing=1.2
            )
            content_group.add(text)
    except Exception as e:
        logger.error(f"创建Markdown内容时发生错误: {e}")
        # 使用简单文本作为后备
        text = Text(
            markdown_text, 
            font_size=38, 
            color=DARK_GRAY,
            font="Arial",
            line_spacing=1.2
        )
        content_group.add(text)
    
    # 垂直排列
    content_group.arrange(DOWN, buff=0.4)
    return content_group


def _get_position_coordinates(position: str) -> np.ndarray:
    """根据位置字符串获取坐标"""
    position_map = {
        'center': ORIGIN,
        'left': LEFT * 3,
        'right': RIGHT * 3,
        'top': UP * 2,
        'bottom': DOWN * 2,
    }
    return position_map.get(position.lower(), ORIGIN)


def _create_placeholder_mobject() -> Mobject:
    """创建占位符对象用于Transform"""
    placeholder = Circle(radius=0.1, color=DARK_GRAY, fill_opacity=0.1)  # 改为深灰色，降低透明度
    placeholder.scale(0.01)  # 让它几乎不可见
    return placeholder


def _apply_transform_style(scene: "FeynmanScene", start_obj: Mobject, end_obj: Mobject, style: str) -> None:
    """应用不同的Transform样式 - 统一使用Transform进行内容切换"""
    playtime = 0.5  # 固定playtime为0.5
    
    if style.lower() == 'fade':
        # 使用Transform实现淡入淡出效果
        scene.play(
            Transform(start_obj, end_obj, run_time=playtime),
            run_time=playtime
        )
    elif style.lower() == 'rotate':
        # 旋转Transform效果
        # 先让目标对象旋转180度作为起始状态
        end_obj.rotate(PI)
        scene.play(
            Transform(start_obj, end_obj, run_time=playtime),
            run_time=playtime
        )
        # 恢复目标对象的正确方向
        end_obj.rotate(-PI)
    elif style.lower() == 'slide':
        # 滑动Transform效果
        # 让目标对象从右侧开始
        end_obj.shift(RIGHT * 6)
        scene.play(
            Transform(start_obj, end_obj, run_time=playtime),
            run_time=playtime
        )
        # 恢复目标对象的正确位置
        end_obj.shift(LEFT * 6)
    elif style.lower() == 'morph':
        # 直接Transform形变
        scene.play(
            Transform(start_obj, end_obj, run_time=playtime),
            run_time=playtime
        )
    else:
        # 默认使用Transform
        scene.play(
            Transform(start_obj, end_obj, run_time=playtime),
            run_time=playtime
        )
    
    # Transform后，start_obj已经变成了end_obj的样子
    # 移除原始对象，添加目标对象以确保正确的引用
    scene.remove(start_obj)
    scene.add(end_obj)


def animate_universal_display(
    scene: "FeynmanScene",
    content_type: str,
    content: Any,
    narration: str,
    title: Optional[str] = None,
    id: Optional[str] = None,
    position: str = "center",
    scale_factor: float = 1.0,
    transform_style: str = "fade",
) -> None:
    """
    通用展示函数，支持文字、表格、图像等多种内容类型的动态展示。
    
    Args:
        scene: Manim场景实例
        content_type: 内容类型（text, table, image, markdown）
        content: 要展示的内容
        narration: 语音旁白文本
        title: 显示标题
        id: Mobject唯一标识符
        position: 内容位置
        scale_factor: 缩放因子
        transform_style: Transform动画样式
    """
    # 创建唯一的引用ID
    if id is None:
        id = f"universal_display_{len(scene.mobjects)}"
    
    logger.info(f"开始创建通用展示内容: {content_type}, id: {id}")
    
    # 清理当前对象
    scene.clear_current_mobj()
    
    try:
        # 根据内容类型创建相应的Mobject
        if content_type.lower() == "text":
            content_obj = _create_text_content(str(content), title)
        elif content_type.lower() == "table":
            if not isinstance(content, dict):
                raise ValueError("表格内容必须是包含headers和data的字典")
            content_obj = _create_table_content(cast(Dict, content), title)
        elif content_type.lower() == "image":
            content_obj = _create_image_content(str(content), title)
        elif content_type.lower() == "markdown":
            content_obj = _create_markdown_content(str(content), title)
        else:
            raise ValueError(f"不支持的内容类型: {content_type}")
        
        # 应用缩放
        if scale_factor != 1.0:
            content_obj.scale(scale_factor)
        
        # 设置位置
        target_position = _get_position_coordinates(position)
        content_obj.move_to(target_position)
        
        # 使用语音旁白包装动画
        with scene.voiceover(narration) as tracker:
            # 如果场景中已有当前对象，进行Transform切换
            if scene.current_mobj is not None:
                # 直接Transform到新内容
                _apply_transform_style(scene, scene.current_mobj, content_obj, transform_style)
            else:
                # 如果没有当前对象，创建一个占位符进行Transform
                placeholder = _create_placeholder_mobject()
                placeholder.move_to(target_position)
                scene.add(placeholder)
                _apply_transform_style(scene, placeholder, content_obj, transform_style)
        
        # 设置当前对象
        scene.current_mobj = content_obj
        
        logger.info(f"成功创建通用展示内容: {content_type}, id: {id}")
        
    except Exception as e:
        logger.error(f"创建通用展示内容失败: {e}")
        # 创建错误提示
        error_text = Text(f"内容加载失败: {content_type}", font_size=36, color=RED)
        error_text.move_to(_get_position_coordinates(position))
        
        with scene.voiceover(narration) as tracker:
            if scene.current_mobj is not None:
                _apply_transform_style(scene, scene.current_mobj, error_text, "fade")
            else:
                placeholder = _create_placeholder_mobject()
                placeholder.move_to(_get_position_coordinates(position))
                scene.add(placeholder)
                _apply_transform_style(scene, placeholder, error_text, "fade")
        
        scene.current_mobj = error_text 