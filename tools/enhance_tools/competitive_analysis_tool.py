#!/usr/bin/env python3
"""
竞品对比分析工具 - 深度洞察
根据内容搜索竞品或类似方案，进行综合对比分析，提供差异化洞察
"""

import json
import os
import re
import requests
from typing import Any, Optional, Dict, List
from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class CompetitiveAnalysisTool(EnhancementTool):
    """竞品对比分析工具 - 深度洞察"""
    
    # 必需的类属性
    tool_name = "competitive_analysis"
    tool_description = "竞品对比分析：搜索并分析相关竞品或类似方案，提供差异化对比洞察"
    tool_category = ToolCategory.DEEP_INSIGHTS
    
    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = [
        "产品介绍和功能说明", "技术方案和架构设计", "工具软件和应用程序",
        "商业模式和服务方案", "算法和技术实现", "平台和系统介绍",
        "开源项目和库", "API和服务接口", "框架和开发工具"
    ]
    suitable_purposes = [
        "展示产品优势和差异化", "技术方案对比选择", "市场竞争分析",
        "功能特性对比", "性能和效果对比", "成本和价值分析",
        "用户体验对比", "技术路线选择", "投资决策支持"
    ]
    required_conditions = [
        "内容涉及具体的产品、技术或方案", "存在明确的竞争对手或替代方案",
        "内容具有可比较的特征和指标", "目标受众需要了解市场选择",
        "内容长度超过800字符以便提取关键信息"
    ]
    
    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = [
        "纯理论知识和概念解释", "历史事件和人物传记", "文学作品和艺术创作",
        "个人经历和感悟分享", "抽象哲学和思想讨论", "基础科学原理",
        "教学课程和培训内容", "新闻资讯和时事评论"
    ]
    unsuitable_purposes = [
        "纯知识传授和教育", "理论学习和概念理解", "娱乐和休闲内容",
        "个人故事分享", "艺术鉴赏和创作", "基础科普教育",
        "历史文化介绍", "情感表达和心理疏导"
    ]
    blocking_conditions = [
        "内容没有明确的竞品或替代方案", "内容过于抽象无法进行具体对比",
        "缺乏可量化的对比维度", "目标受众不关心市场选择",
        "内容长度不足以提取有效信息", "网络搜索功能不可用"
    ]

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        self.search_enabled = False
        if config:
            self._init_model()
            self._init_search()
    
    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType
            
            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )
            
            system_prompt = """你是专业的竞品分析专家，擅长识别、搜索和对比分析相关产品、技术方案和服务。
你的任务是：
1. 准确识别内容中的核心产品/技术/方案
2. 判断是否存在有价值的竞品对比空间
3. 生成精准的搜索关键词
4. 基于搜索结果进行深度对比分析
5. 提供客观、有洞察力的差异化分析

请严格按照要求的JSON格式输出，确保分析的准确性和实用性。"""
            
            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def _init_search(self):
        """初始化搜索功能"""
        try:
            # 这里可以集成多种搜索API，比如Google Search API, Bing Search API等
            # 暂时使用简单的网络搜索模拟
            self.search_enabled = True
            logger.info("搜索功能初始化成功")
        except Exception as e:
            logger.warning(f"搜索功能初始化失败: {e}")

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "竞品对比分析报告",
            "typical_output": "包含竞品识别、特性对比、优劣分析、市场定位等结构化对比数据",
            "use_case": "当内容涉及具体产品、技术或方案，且存在明确竞争对手时，自动搜索并生成深度对比分析"
        }

    def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
        """检查工具是否可用 - 重点检查是否存在对比空间"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config and 
            len(content) >= 800 and  # 需要足够的内容来分析
            self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True) and
            self.agent is not None and
            self.search_enabled
        )
        
        if not basic_check:
            return False
        
        # 核心检查：是否存在对比空间
        return self._has_comparison_potential(content, purpose)
    
    def _has_comparison_potential(self, content: str, purpose: str) -> bool:
        """检查内容是否具有竞品对比的潜力"""
        try:
            # 使用LLM快速判断是否存在对比空间
            check_prompt = f"""请分析以下内容是否适合进行竞品对比分析：

**内容**：
{content[:2000]}{'...(内容截断)' if len(content) > 2000 else ''}

**目的**：{purpose}

**判断标准**：
1. 是否涉及具体的产品、技术、工具或方案？
2. 是否存在明确的竞争对手或替代方案？
3. 是否具有可对比的特征和指标？
4. 对比分析是否对目标受众有价值？

请简洁回答：YES或NO，并在同一行简要说明理由（限制在50字内）。

格式示例：YES - 涉及具体产品Quarkdown，存在多个竞品可对比"""

            response = self.agent.step(check_prompt)
            response_content = response.msgs[0].content.strip()
            
            logger.info(f"竞品对比检查响应: {response_content[:200]}...")
            
            # 解析响应 - 支持多种格式
            response_upper = response_content.upper()
            
            # 方法1: 检查是否包含YES
            if "YES" in response_upper:
                logger.info(f"竞品对比检查通过: 响应包含YES")
                return True
            
            # 方法2: 检查JSON格式中的"适合竞品分析"字段
            try:
                import json
                # 尝试解析JSON
                if response_content.startswith('{'):
                    data = json.loads(response_content)
                    suitable_value = data.get("适合竞品分析", "").upper()
                    if suitable_value == "YES":
                        logger.info(f"竞品对比检查通过: JSON格式显示适合")
                        return True
            except:
                pass
            
            # 方法3: 检查是否以YES开头
            if response_content.upper().startswith('YES'):
                logger.info(f"竞品对比检查通过: 以YES开头")
                return True
            
            # 如果都没有匹配，则认为不通过
            logger.info(f"竞品对比检查未通过: {response_content[:100]}...")
            return False
                
        except Exception as e:
            logger.error(f"竞品对比潜力检查失败: {e}")
            return False

    def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """应用竞品对比分析工具"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🔧 应用{self.tool_name}工具，准备进行竞品对比分析")

        try:
            os.makedirs(output_dir, exist_ok=True)
            
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)
            
            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"竞品对比分析结果已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行竞品对比分析
            result_data = self._process_competitive_analysis(content, context)
            
            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_competitive_analysis(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行竞品对比分析的核心逻辑"""
        try:
            # 第一步：分析内容并生成搜索关键词
            keywords_data = self._extract_search_keywords(content, context)
            if not keywords_data:
                return None
            
            # 第二步：搜索竞品信息
            search_results = self._search_competitors(keywords_data.get("search_keywords", []))
            
            # 第三步：生成对比分析报告
            analysis_result = self._generate_competitive_analysis(content, context, keywords_data, search_results)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"竞品对比分析处理失败: {e}")
            return None

    def _extract_search_keywords(self, content: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """提取搜索关键词和产品信息"""
        purpose = context.get("purpose", "竞品对比分析")
        
        prompt = f"""基于以下内容，提取关键信息并生成搜索关键词：

**内容材料**：
{content[:6000]}{'...(内容截断)' if len(content) > 6000 else ''}

**分析目标**：{purpose}

请提取以下信息并严格按照JSON格式输出：

```json
{{
    "target_product": {{
        "name": "主要产品/技术/方案名称",
        "category": "产品类别",
        "key_features": ["核心特性1", "核心特性2", "核心特性3"],
        "use_cases": ["使用场景1", "使用场景2"]
    }},
    "search_keywords": [
        "产品名称 + competitors",
        "产品名称 + alternatives", 
        "产品类别 + comparison",
        "产品名称 + vs",
        "similar to + 产品名称"
    ],
    "comparison_dimensions": [
        "功能特性", "性能表现", "使用体验", "成本价格", "技术架构", "适用场景"
    ],
    "analysis_focus": "对比分析的重点关注领域"
}}
```"""

        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content
            
            # 提取JSON
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            logger.info("搜索关键词提取成功")
            return result_data
            
        except Exception as e:
            logger.error(f"搜索关键词提取失败: {e}")
            return None

    def _search_competitors(self, keywords: List[str]) -> List[Dict[str, Any]]:
        """搜索竞品信息"""
        search_results = []
        
        try:
            # 模拟搜索结果 - 实际实现中应该调用真实的搜索API
            for keyword in keywords[:3]:  # 限制搜索数量
                # 这里应该调用实际的搜索API，比如Google Search API
                # 暂时使用模拟数据
                mock_result = {
                    "keyword": keyword,
                    "results": [
                        {
                            "title": f"关于{keyword}的搜索结果1",
                            "url": "https://example.com/1",
                            "snippet": f"这是关于{keyword}的详细介绍和对比分析内容..."
                        },
                        {
                            "title": f"关于{keyword}的搜索结果2", 
                            "url": "https://example.com/2",
                            "snippet": f"另一个关于{keyword}的竞品分析和评测内容..."
                        }
                    ]
                }
                search_results.append(mock_result)
            
            logger.info(f"搜索完成，获得{len(search_results)}组结果")
            return search_results
            
        except Exception as e:
            logger.error(f"竞品搜索失败: {e}")
            return []

    def _generate_competitive_analysis(self, content: str, context: Dict[str, Any], 
                                     keywords_data: Dict[str, Any], 
                                     search_results: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """生成竞品对比分析报告"""
        purpose = context.get("purpose", "竞品对比分析")
        
        # 构建搜索结果摘要
        search_summary = ""
        for result in search_results:
            search_summary += f"搜索关键词: {result['keyword']}\n"
            for item in result.get('results', [])[:2]:
                search_summary += f"- {item['title']}: {item['snippet'][:200]}...\n"
            search_summary += "\n"
        
        prompt = f"""基于原始内容和搜索到的竞品信息，生成深度对比分析报告：

**原始内容**：
{content[:4000]}{'...(内容截断)' if len(content) > 4000 else ''}

**目标产品信息**：
{json.dumps(keywords_data.get('target_product', {}), ensure_ascii=False, indent=2)}

**搜索到的竞品信息**：
{search_summary[:3000]}{'...(内容截断)' if len(search_summary) > 3000 else ''}

**分析目标**：{purpose}

请生成结构化的竞品对比分析报告，严格按照以下JSON格式输出：

```json
{{
    "analysis_summary": {{
        "target_product": "目标产品名称",
        "competitor_count": "发现的竞品数量", 
        "analysis_scope": "对比分析的范围",
        "key_insights": "核心洞察摘要"
    }},
    "competitors": [
        {{
            "name": "竞品名称1",
            "category": "产品类别",
            "positioning": "市场定位",
            "key_features": ["特性1", "特性2", "特性3"],
            "advantages": ["优势1", "优势2"],
            "disadvantages": ["劣势1", "劣势2"]
        }}
    ],
    "key_comparison_points": {{
        "功能特性对比": [
            "目标产品：具备特性A、B、C",
            "竞品1：主要特性X、Y，缺少C",
            "竞品2：特性Y、Z较强，但A功能较弱"
        ],
        "技术架构对比": [
            "目标产品：采用现代化架构，扩展性好",
            "竞品1：传统架构，稳定但灵活性有限",
            "竞品2：混合架构，性能优化较好"
        ],
        "用户体验对比": [
            "目标产品：界面简洁，学习成本低",
            "竞品1：功能丰富但复杂度高",
            "竞品2：专业工具，适合技术用户"
        ],
        "市场定位对比": [
            "目标产品：面向中小企业，性价比高",
            "竞品1：企业级解决方案，价格较高",
            "竞品2：开源免费，但需要技术支持"
        ]
    }},
    "differentiation_analysis": {{
        "unique_advantages": ["目标产品的独特优势1", "独特优势2"],
        "competitive_gaps": ["相对竞品的不足1", "不足2"],
        "market_opportunities": ["市场机会1", "市场机会2"],
        "strategic_recommendations": ["战略建议1", "战略建议2"]
    }},
    "market_landscape": {{
        "market_size": "市场规模评估",
        "growth_trend": "增长趋势",
        "key_players": ["主要参与者1", "主要参与者2"],
        "competitive_intensity": "竞争激烈程度评估"
    }}
}}
```"""

        try:
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content
            
            # 提取JSON
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            
            # 添加元数据
            result_data["metadata"] = {
                "generation_time": "2024-01-01",
                "search_keywords": keywords_data.get("search_keywords", []),
                "analysis_focus": keywords_data.get("analysis_focus", ""),
                "data_quality": "基于网络搜索和AI分析生成"
            }
            
            logger.info("竞品对比分析报告生成成功")
            return result_data
            
        except Exception as e:
            logger.error(f"竞品对比分析报告生成失败: {e}")
            return None

    def generate_intro(self, tool_result: Dict[str, Any]) -> str:
        """生成竞品对比分析的介绍文本"""
        data = tool_result.get("data", {})
        
        if not data:
            return ""

        analysis_summary = data.get("analysis_summary", {})
        competitors = data.get("competitors", [])
        key_comparison_points = data.get("key_comparison_points", {})
        differentiation = data.get("differentiation_analysis", {})
        
        intro = f"## 🔍 {self.tool_description}\n\n"
        
        # 分析概览
        if analysis_summary:
            intro += f"**目标产品**: {analysis_summary.get('target_product', 'N/A')}\n"
            intro += f"**发现竞品**: {analysis_summary.get('competitor_count', 'N/A')}个\n"
            intro += f"**分析范围**: {analysis_summary.get('analysis_scope', 'N/A')}\n\n"
            intro += f"**核心洞察**: {analysis_summary.get('key_insights', 'N/A')}\n\n"
        
        # 主要竞品
        if competitors:
            intro += "### 主要竞品概览\n"
            for comp in competitors[:3]:  # 显示前3个竞品
                intro += f"- **{comp.get('name', 'N/A')}**: {comp.get('positioning', 'N/A')}\n"
            intro += "\n"
        
        # 核心对比要点
        if key_comparison_points:
            intro += "### 核心对比要点\n"
            for dimension, points in key_comparison_points.items():
                if points:  # 确保有对比点
                    intro += f"**{dimension}**:\n"
                    for point in points[:2]:  # 只显示前2个要点
                        intro += f"- {point}\n"
                    intro += "\n"
        
        # 差异化分析
        if differentiation:
            unique_advantages = differentiation.get('unique_advantages', [])
            if unique_advantages:
                intro += "### 独特优势\n"
                for advantage in unique_advantages[:2]:
                    intro += f"- {advantage}\n"
                intro += "\n"
        
        intro += "通过深度竞品对比分析，为您提供全面的市场洞察和差异化定位建议。\n"
        
        return intro 