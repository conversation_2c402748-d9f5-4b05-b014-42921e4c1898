#!/usr/bin/env python3
"""
架构图生成工具 - 多模态呈现类
基于ExcalidrawToolkit生成架构图视频，用于可视化系统架构和流程图
"""

import json
import os
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class ArchitectureDiagramTool(EnhancementTool):
    """架构图生成工具 - 多模态呈现类"""

    tool_name = "architecture_diagram"
    tool_description = "基于ExcalidrawToolkit生成架构图视频，用于可视化系统架构和流程图"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = ["系统架构介绍", "技术方案说明", "流程图描述", "组件关系说明", "数据流介绍"]
    suitable_purposes = ["架构可视化", "系统设计展示", "流程说明", "技术方案演示", "组件关系图解"]
    required_conditions = ["内容包含架构或流程描述", "内容长度>500字符", "包含组件或步骤信息"]

    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = ["纯文本理论", "数学公式", "代码片段", "个人观点", "抽象概念"]
    unsuitable_purposes = ["理论教学", "数学推导", "代码展示", "文本阅读", "概念解释"]
    blocking_conditions = ["内容过短", "缺乏架构信息", "无组件关系描述"]

    def __init__(self, config=None):
        self.config = config
        self.toolkit = None
        if config:
            self._init_toolkit()

    def _init_toolkit(self):
        """初始化ExcalidrawToolkit"""
        try:
            from tools.excalidraw_toolkit import ExcalidrawToolkit

            self.toolkit = ExcalidrawToolkit()
            logger.info(f"{self.tool_name}工具包初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}工具包初始化失败: {e}")

    def get_tool_info(self) -> dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "架构图视频配置",
            "typical_output": "生成animate_architecture_diagram所需的配置内容",
            "use_case": "为系统架构、技术方案、流程图等内容生成可视化图表",
        }

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 500  # 架构图需要足够的内容描述
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 检查toolkit是否初始化成功
        if not self.toolkit:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_content(content, context)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """处理内容的核心逻辑 - 生成架构图配置"""
        purpose = context.get("purpose", "架构图生成")

        # 提取架构描述信息
        architecture_description = self._extract_architecture_description(content)

        if not architecture_description:
            logger.warning("未能从内容中提取到有效的架构描述")
            return None

        # 生成配置数据
        config_data = {
            "summary": {
                "content_theme": "系统架构可视化",
                "target_audience": purpose,
                "processing_focus": "架构图生成和动画配置",
            },
            "architecture_config": {
                "content_description": architecture_description,
                "animation_type": "animate_architecture_diagram",
                "narration": self._generate_narration(architecture_description, purpose),
                "id": f"arch_diagram_{abs(hash(content)) % 10000}",
            },
            "metadata": {
                "processing_time": "实时生成",
                "data_quality": "基于内容自动提取",
                "content_length": len(content),
                "description_length": len(architecture_description),
            },
        }

        return config_data

    def _extract_architecture_description(self, content: str) -> str:
        """从内容中提取架构描述信息"""
        # 简化的架构信息提取逻辑
        # 寻找包含架构、系统、组件、流程等关键词的段落

        architecture_keywords = [
            "架构",
            "系统",
            "组件",
            "模块",
            "服务",
            "流程",
            "步骤",
            "前端",
            "后端",
            "数据库",
            "API",
            "接口",
            "网关",
            "微服务",
            "处理",
            "分析",
            "存储",
            "传输",
            "连接",
            "交互",
            "调用",
        ]

        # 分段处理内容
        paragraphs = content.split("\n")
        relevant_paragraphs = []

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if len(paragraph) < 20:  # 跳过太短的段落
                continue

            # 检查段落是否包含架构相关关键词
            keyword_count = sum(1 for keyword in architecture_keywords if keyword in paragraph)
            if keyword_count >= 2:  # 至少包含2个关键词
                relevant_paragraphs.append(paragraph)

        if relevant_paragraphs:
            # 合并相关段落，限制长度
            description = "\n".join(relevant_paragraphs)
            if len(description) > 2000:  # 限制描述长度
                description = description[:2000] + "..."
            return description
        else:
            # 如果没有找到特定的架构段落，使用整个内容的前部分
            return content[:1500] + "..." if len(content) > 1500 else content

    def _generate_narration(self, description: str, purpose: str) -> str:
        """生成旁白文本"""
        # 基于描述和目的生成简单的旁白
        if "微服务" in description:
            return f"这个架构图展示了微服务系统的整体结构，帮助理解{purpose}中各组件的关系和数据流。"
        elif "数据" in description and ("流程" in description or "处理" in description):
            return "这个流程图展示了数据处理的完整路径，从数据收集到最终分析的各个环节。"
        elif "前端" in description and "后端" in description:
            return "这个架构图展示了前后端分离的系统设计，以及各层之间的交互关系。"
        else:
            return f"这个架构图展示了系统的整体设计和组件关系，帮助理解{purpose}的技术实现。"

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        architecture_config = data.get("architecture_config", {})
        content_description = architecture_config.get("content_description", "")
        narration = architecture_config.get("narration", "")
        diagram_id = architecture_config.get("id", "")

        intro = f"## 🏗️ {self.tool_description}\n\n"
        intro += f"**架构图ID**: {diagram_id}\n\n"
        intro += f"**描述长度**: {len(content_description)} 字符\n\n"
        intro += "**动画配置**:\n"
        intro += "```json\n"
        intro += "{\n"
        intro += '  "type": "animate_architecture_diagram",\n'
        intro += '  "params": {\n'
        intro += f'    "content_description": "{content_description[:100]}...",\n'
        intro += f'    "narration": "{narration}",\n'
        intro += f'    "id": "{diagram_id}"\n'
        intro += "  }\n"
        intro += "}\n"
        intro += "```\n\n"
        intro += "**使用说明**: 上述配置可直接用于animate_architecture_diagram函数，生成交互式架构图动画。\n\n"

        return intro
