#!/usr/bin/env python3
"""
Mermaid图表生成工具 - 多模态呈现类
基于内容自动生成Mermaid图表代码并渲染为图像，支持流程图、架构图等多种图表类型
"""

import json
import os
import re
import subprocess
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class MermaidDiagramTool(EnhancementTool):
    """Mermaid图表生成工具 - 多模态呈现类"""

    tool_name = "mermaid_diagram"
    tool_description = "基于内容自动生成Mermaid图表代码并渲染为图像，支持流程图、架构图等多种图表类型"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION

    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = ["流程描述", "系统架构", "组织结构", "时序图描述", "状态转换", "数据流程", "决策树"]
    suitable_purposes = ["流程可视化", "架构图解", "关系展示", "步骤说明", "逻辑梳理", "系统设计"]
    required_conditions = ["内容包含流程或关系描述", "内容长度>300字符", "包含步骤或组件信息"]

    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = ["纯文本叙述", "数学公式", "代码实现", "理论概念", "个人观点"]
    unsuitable_purposes = ["文本阅读", "数学推导", "代码展示", "理论教学", "观点表达"]
    blocking_conditions = ["内容过短", "缺乏流程信息", "无关系描述", "纯理论内容"]

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型 - 用于智能生成Mermaid代码"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 定制化系统提示词
            system_prompt = """你是专业的Mermaid图表生成专家，负责根据内容描述生成准确的Mermaid代码。
请严格按照Mermaid语法规范，确保生成的代码可以正确渲染。支持flowchart、sequence、class、state等多种图表类型。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "Mermaid图表和图像文件",
            "typical_output": "生成Mermaid代码文件和对应的PNG图像",
            "use_case": "为包含流程、架构、关系的内容生成可视化图表",
        }

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 300  # Mermaid图表需要足够的内容描述
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🔧 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_content(content, context)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🔧 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """使用Camel进行智能Mermaid代码生成"""
        purpose = context.get("purpose", "图表生成")

        # 构建结构化提示词
        prompt = f"""基于以下内容生成合适的Mermaid图表代码：

**内容材料**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**处理目标**：{purpose}

**输出要求**：
请分析内容并生成1-3个合适的Mermaid图表。每个图表应该：
1. 选择合适的图表类型（flowchart、sequence、class、state、graph等）
2. 提供语义化的文件名
3. 包含准确的Mermaid代码
4. 提供图表描述

请严格按照以下JSON格式输出：

```json
{{
    "summary": {{
        "content_theme": "内容主题概括",
        "target_audience": "{purpose}",
        "processing_focus": "图表生成重点"
    }},
    "diagrams": [
        {{
            "type": "flowchart",
            "title": "图表标题",
            "description": "图表内容描述",
            "semantic_name": "semantic_file_name",
            "mermaid_code": "flowchart TD\\n    A[开始] --> B[处理]\\n    B --> C[结束]"
        }}
    ],
    "metadata": {{
        "diagram_count": 1,
        "data_quality": "数据质量评估"
    }}
}}
```"""

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个响应
                json_str = response_content.strip()

            result_data = json.loads(json_str)

            # 渲染图表
            diagrams = result_data.get("diagrams", [])
            if diagrams:
                return self._render_diagrams(diagrams, context, result_data)
            else:
                logger.warning("未生成任何图表")
                return None

        except Exception as e:
            logger.error(f"Camel Mermaid代码生成失败: {e}")
            return None

    def _render_diagrams(
        self, diagrams: list[dict[str, Any]], context: dict[str, Any], base_data: Optional[dict[str, Any]] = None
    ) -> Optional[dict[str, Any]]:
        """渲染Mermaid图表为图像文件"""
        rendered_diagrams = []

        for i, diagram in enumerate(diagrams):
            try:
                # 生成语义化文件名
                semantic_name = diagram.get("semantic_name", f"diagram_{i+1}")
                semantic_name = re.sub(r"[^\w\-_]", "_", semantic_name)  # 清理文件名

                # 创建Mermaid文件
                mmd_filename = f"{semantic_name}.mmd"
                mmd_path = os.path.join("output", mmd_filename)

                # 确保输出目录存在
                os.makedirs(os.path.dirname(mmd_path), exist_ok=True)

                # 写入Mermaid代码
                mermaid_code = diagram.get("mermaid_code", "")
                with open(mmd_path, "w", encoding="utf-8") as f:
                    f.write(mermaid_code)

                # 生成PNG图像
                png_filename = f"{semantic_name}.png"
                png_path = os.path.join("output", png_filename)

                # 使用mmdc命令渲染图像
                success = self._render_mermaid_to_png(mmd_path, png_path)

                if success:
                    rendered_diagram = {
                        "type": diagram.get("type", "unknown"),
                        "title": diagram.get("title", ""),
                        "description": diagram.get("description", ""),
                        "semantic_name": semantic_name,
                        "mermaid_file": mmd_path,
                        "image_file": png_path,
                        "mermaid_code": mermaid_code,
                        "status": "success",
                    }
                    rendered_diagrams.append(rendered_diagram)
                    logger.info(f"✅ 成功渲染图表: {semantic_name}")
                else:
                    logger.warning(f"⚠️ 图表渲染失败: {semantic_name}")

            except Exception as e:
                logger.error(f"渲染图表时出错: {e}")
                continue

        if not rendered_diagrams:
            return None

        # 构建最终结果
        result_data = base_data or {}
        result_data.update(
            {
                "rendered_diagrams": rendered_diagrams,
                "diagram_count": len(rendered_diagrams),
                "processing_status": "completed",
            }
        )

        return result_data

    def _render_mermaid_to_png(self, mmd_path: str, png_path: str) -> bool:
        """使用mmdc命令将Mermaid文件渲染为PNG图像"""
        try:
            # 构建mmdc命令
            cmd = ["mmdc", "-i", mmd_path, "-o", png_path, "-w", "2160", "-s", "4"]

            # 执行命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                logger.info(f"成功渲染Mermaid图表: {png_path}")
                return True
            else:
                logger.error(f"mmdc命令执行失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error(f"mmdc命令执行超时: {mmd_path}")
            return False
        except FileNotFoundError:
            logger.error("mmdc命令未找到，请确保已安装mermaid-cli")
            return False
        except Exception as e:
            logger.error(f"执行mmdc命令时出错: {e}")
            return False

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        rendered_diagrams = data.get("rendered_diagrams", [])
        diagram_count = data.get("diagram_count", 0)

        intro = f"## 📊 {self.tool_description}\n\n"
        intro += f"**生成图表数量**: {diagram_count}\n\n"

        if rendered_diagrams:
            intro += "### 生成的图表\n\n"
            for diagram in rendered_diagrams:
                title = diagram.get("title", "未命名图表")
                description = diagram.get("description", "")
                image_file = diagram.get("image_file", "")
                diagram_type = diagram.get("type", "unknown")

                intro += f"#### {title}\n\n"
                intro += f"**类型**: {diagram_type}\n\n"
                if description:
                    intro += f"**描述**: {description}\n\n"

                if image_file and os.path.exists(image_file):
                    intro += f"![{title}]({image_file})\n\n"
                    intro += f"**图片路径**: `{image_file}`\n\n"
                else:
                    intro += f"**图片文件**: {image_file} (文件不存在)\n\n"

                intro += "---\n\n"

        intro += "**使用说明**: 上述图表已生成为PNG图像，可直接在Markdown中使用。\n\n"

        return intro
