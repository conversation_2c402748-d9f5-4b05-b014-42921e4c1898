#!/usr/bin/env python3
"""
录屏工具 - 多模态呈现类
专门用于开场基本信息视频介绍
"""

import os
import re
from typing import Any, Optional, Dict
from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class ScreenRecordingTool(EnhancementTool):
    """录屏工具 - 多模态呈现类，专门用于开场基本信息视频介绍"""
    
    tool_name = "screen_recording"
    tool_description = "自动录制config.yaml中配置的URL页面，生成开场介绍视频。注意：这个工具只能录制配置文件中指定的URL，不会录制用户输入材料中提到的其他URL"
    tool_category = ToolCategory.MULTIMODAL_PRESENTATION
    
    suitable_content_types = ["任何基于配置文件URL源的内容分析", "GitHub项目介绍", "ArXiv论文解读", "网页内容分析"]
    suitable_purposes = ["视频开场介绍", "项目概览视频", "论文介绍视频", "演示视频制作", "教学视频开场"]
    required_conditions = ["config.yaml的material.sources中有enabled=true的源", "该源配置了有效的URL地址", "material_enhance.screen_record=true"]
    
    # 不适用场景描述  
    unsuitable_content_types = ["纯本地文件分析", "chat模式对话内容", "无配置URL的纯文本内容"]
    unsuitable_purposes = ["详细技术教程", "深度代码分析", "纯音频制作", "静态文档生成"]
    blocking_conditions = ["config中material.sources都为disabled", "启用的源没有配置URL", "screen_record配置为false", "用户明确禁用录屏"]

    def __init__(self, config=None):
        self.config = config
        self._initialize_toolkits()

    def _initialize_toolkits(self):
        """初始化录屏工具包（延迟导入）"""
        self.github_toolkit = None
        self.arxiv_toolkit = None

        try:
            from tools.arxiv_recorder_toolkit import ArxivRecorderToolkit
            from tools.github_scroller_toolkit import GithubScrollerToolkit

            self.github_toolkit = GithubScrollerToolkit()
            self.arxiv_toolkit = ArxivRecorderToolkit()
            logger.info("录屏工具包初始化成功")
        except ImportError as e:
            logger.warning(f"录屏工具包导入失败: {e}")

    def get_tool_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes, 
            "blocking_conditions": self.blocking_conditions,
            "output_type": "视频文件(MP4)",
            "typical_duration": "8-12秒",
            "use_case": "专门录制config.yaml中material.sources里配置的URL页面。只要配置文件中有启用的URL源（如GitHub、ArXiv等），就强烈推荐使用此工具制作开场视频。注意：此工具不处理用户输入内容中提到的URL，只处理配置文件中的URL设置。"
        }

    def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 检查基本配置
        if not self.config:
            return False

        # 检查config中是否启用录屏
        material_enhance = self.config.get("material", {}).get("material_enhance", {})
        if not material_enhance.get("screen_record", False):
            return False

        # 获取当前启用的源和URL
        sources = self.config.get("material", {}).get("sources", {})
        for source_type, source_config in sources.items():
            if source_config.get("enabled", False):
                url = source_config.get("url", "")
                if url and url.startswith(("http://", "https://")):
                    return True
        
        return False

    def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """执行录屏工具"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        # 获取URL和源类型
        sources = self.config.get("material", {}).get("sources", {})
        for source_type, source_config in sources.items():
            if source_config.get("enabled", False):
                url = source_config.get("url", "")
                if url and url.startswith(("http://", "https://")):
                    break
        else:
            return None
        
        source_type = self._determine_source_type(url)
        
        # 转换ArXiv PDF链接为页面链接
        if source_type == "arxiv" and "/pdf/" in url:
            url = url.replace("/pdf/", "/abs/")

        # 生成输出路径
        video_filename = "screen_record.mp4"
        video_path = os.path.join(output_dir, video_filename)
        os.makedirs(output_dir, exist_ok=True)

        # 检查是否已存在文件
        if os.path.exists(video_path):
            logger.info(f"录屏视频已存在: {video_path}")
            return {
                "tool_name": self.tool_name,
                "type": "video_recording",
                "source_type": source_type,
                "url": url,
                "file_path": video_path,
                "full_path": video_path,
                "status": "exists",
            }

        # 执行录屏
        try:
            result = self._record_video(url, video_path, source_type)
            if result and result.get("status") == "success":
                return {
                    "tool_name": self.tool_name,
                    "type": "video_recording",
                    "source_type": source_type,
                    "url": url,
                    "file_path": video_path,
                    "full_path": video_path,
                    "status": "created",
                }
        except Exception as e:
            logger.error(f"录屏过程异常: {e}")

        return None

    def _determine_source_type(self, url: str) -> str:
        """确定录屏源类型"""
        if "github.com" in url:
            return "github"
        elif "arxiv.org" in url:
            return "arxiv"
        else:
            return "github"  # 默认使用github录屏方式

    def _record_video(self, url: str, output_path: str, source_type: str) -> Optional[dict]:
        """执行实际的录屏操作"""
        if source_type == "github" and self.github_toolkit:
            return self.github_toolkit.record_github_scroll_video(
                url=url,
                output_path=output_path,
                duration=12,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=1,
                star_focus=2,
                zoom_factor=2.0,
                readme_pause=1.0,
            )
        elif source_type == "arxiv" and self.arxiv_toolkit:
            return self.arxiv_toolkit.record_arxiv_video(
                url=url,
                output_path=output_path,
                duration=8,
                width=1920,
                height=1080,
                fps=15,
                smooth_factor=0.2,
                title_focus=4,
                zoom_factor=2.0,
                abstract_pause=4.0,
            )
        return None

    def generate_intro(self, tool_result: Dict[str, Any]) -> str:
        """生成录屏视频的介绍内容"""
        source_type = tool_result.get("source_type")
        url = tool_result.get("url")
        file_path = tool_result.get("file_path")

        # 统一的开场标识
        opening_emoji = "🎬"
        
        if source_type == "github":
            # 提取GitHub项目信息
            match = re.search(r"github\.com/([\w\-]+)/([\w\-]+)", url)
            if match:
                owner, repo = match.groups()
                intro = f"## {opening_emoji} 开场视频介绍\n\n"
                intro += f"这是 **{owner}/{repo}** 项目的开场介绍视频，"
                intro += "为您快速展示项目的核心信息、界面布局和基本特点，是了解该项目的最佳起点。\n\n"
                intro += f"📺 **[{repo} 项目开场介绍]({file_path})**\n\n"
                intro += "*💡 建议：这个开场视频将帮助您在深入了解项目细节之前，先建立整体印象*\n\n"
            else:
                intro = f"## {opening_emoji} 开场视频介绍\n\n"
                intro += "为您展示项目的基本信息和整体概况\n\n"
                intro += f"📺 **[项目开场介绍]({file_path})**\n\n"
        elif source_type == "arxiv":
            # 提取ArXiv论文信息
            match = re.search(r"(\d{4}\.\d{5})", url)
            if match:
                paper_id = match.group(1)
                intro = f"## {opening_emoji} 论文开场介绍\n\n"
                intro += f"这是论文 **{paper_id}** 的开场介绍视频，"
                intro += "展示论文的标题、作者、摘要等核心信息，帮助您快速了解论文的基本情况和研究主题。\n\n"
                intro += f"📺 **[论文 {paper_id} 开场介绍]({file_path})**\n\n"
                intro += "*💡 建议：通过这个开场视频，您可以在阅读全文之前快速判断论文的相关性*\n\n"
            else:
                intro = f"## {opening_emoji} 论文开场介绍\n\n"
                intro += "展示论文的基本信息和研究概况\n\n"
                intro += f"📺 **[论文开场介绍]({file_path})**\n\n"
        else:
            intro = f"## {opening_emoji} 内容开场介绍\n\n"
            intro += "这是内容的开场介绍视频，为您快速展示主要信息和基本概况。\n\n"
            intro += f"📺 **[内容开场介绍]({file_path})**\n\n"
            intro += "*💡 这个开场视频将为您提供内容的整体印象*\n\n"

        return intro 