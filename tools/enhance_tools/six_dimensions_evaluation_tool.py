#!/usr/bin/env python3
"""
六维度评估工具 - 深度洞察类
基于六大维度对功能案例型内容进行全面评估，生成量化评分和雷达图
"""

import json
import os
import re
from typing import Any, Optional, Dict
from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class SixDimensionsEvaluationTool(EnhancementTool):
    """六维度评估工具 - 深度洞察类"""
    
    tool_name = "six_dimensions_evaluation"
    tool_description = "基于六大维度对功能案例型内容进行全面评估，生成量化评分和雷达图"
    tool_category = ToolCategory.DEEP_INSIGHTS
    
    suitable_content_types = ["GitHub项目分析", "开源软件评估", "技术产品介绍"]
    suitable_purposes = ["项目质量评估", "技术选型参考", "产品竞争力分析"]
    required_conditions = ["内容长度>800字符", "包含技术实现信息"]
    unsuitable_content_types = ["纯理论概念", "学术论文", "抽象讨论"]
    unsuitable_purposes = ["理论教学", "概念解释", "学术研究"]
    blocking_conditions = ["内容过短", "缺乏技术细节"]

    def __init__(self, config=None):
        self.config = config
        self.evaluator_agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType
            
            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )
            
            self.evaluator_agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", "你是六维度评估专家"),
                model=self.model,
            )
        except Exception as e:
            logger.warning(f"模型初始化失败: {e}")

    def get_tool_info(self) -> Dict[str, Any]:
        return {
            "name": self.tool_name, "description": self.tool_description,
            "category": self.tool_category.value, "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes, "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types, "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions, "output_type": "雷达图+报告",
            "use_case": "评估技术项目的综合质量"
        }

    def can_apply(self, content: str, purpose: str, context: Dict[str, Any]) -> bool:
        return (self.config and self.evaluator_agent and len(content) >= 800 and 
                self.config.get("material", {}).get("material_enhance", {}).get("six_dimensions_evaluation", True))

    def apply_tool(self, content: str, output_dir: str, context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        try:
            os.makedirs(output_dir, exist_ok=True)
            
            # 使用内置评估模板
            prompt = f"""基于以下内容进行六维度评估，每个维度给出1-5分的评分和简要评语。

内容: {content[:5000]}
目标: {context.get('purpose', '评估')}

评分标准：1分-严重不足，2分-不足，3分-一般，4分-良好，5分-优秀。

请严格按照以下JSON格式输出，不要添加任何其他文字：
{{"dimensions": {{"核心功能完善性": {{"score": 4, "comment": "功能完善"}}, "可用性与易用性": {{"score": 3, "comment": "易用性一般"}}, "项目活跃度": {{"score": 4, "comment": "更新活跃"}}, "代码质量": {{"score": 5, "comment": "代码优秀"}}, "架构设计": {{"score": 4, "comment": "设计合理"}}, "文档完备性": {{"score": 3, "comment": "文档完整"}}}}, "total_score": 23, "overall_grade": "B"}}"""
            
            response = self.evaluator_agent.step(prompt)
            result_text = response.msgs[0].content
            
            # 提取JSON
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if not json_match:
                return None
            
            try:
                data = json.loads(json_match.group(0))
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败: {e}, 原始响应: {result_text[:200]}...")
                return None
            
            # 生成文件
            radar_path = self._save_radar_chart(data, output_dir)
            report_path = self._save_report(data, output_dir)
            
            return {
                "tool_name": self.tool_name, "type": "six_dimensions_evaluation",
                "radar_chart_path": radar_path, "evaluation_report_path": report_path,
                "status": "success", "metadata": {"total_score": data.get("total_score", 0)},
                "evaluation_data": data  # 评估数据用于generate_intro方法
            }
            
        except Exception as e:
            logger.error(f"评估工具执行失败: {e}")
            return None

    def _save_radar_chart(self, data: Dict[str, Any], output_dir: str) -> str:
        """生成雷达图数据的markdown格式"""
        path = os.path.join(output_dir, "six_dimensions_radar_chart.md")
        dimensions = data.get("dimensions", {})
        
        # 构建markdown内容
        content = f"""# 六维度评估雷达图数据

## 基本信息
- **标题**: 六维度评估 (总分:{data.get('total_score', 0)}/30)
- **综合评级**: {data.get('overall_grade', 'C')}
- **图表类型**: 雷达图
- **动画风格**: grow

## 雷达图JSON数据

```json
{{
  "chart_type": "radar",
  "data": [
    {{
{', '.join([f'      "{dim}": {info.get("score", 0)}' for dim, info in dimensions.items()])}
    }}
  ],
  "dataset_names": [
    "评分"
  ],
  "title": "六维度评估 (总分:{data.get('total_score', 0)}/30)",
  "animation_style": "grow",
  "narration": "综合评级: {data.get('overall_grade', 'C')}"
}}
```

## 各维度评分详情

| 维度 | 评分 | 星级显示 |
|------|------|----------|"""
        
        for dim, info in dimensions.items():
            score = info.get("score", 0)
            stars = "★" * score + "☆" * (5 - score)
            content += f"\n| {dim} | {score}/5 | {stars} |"
        
        content += "\n\n## 使用说明\n\n"
        content += "上述JSON数据可以直接用于雷达图可视化组件，支持动态渲染和交互展示。\n"
        
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        return path

    def _save_report(self, data: Dict[str, Any], output_dir: str) -> str:
        """生成评估报告"""
        path = os.path.join(output_dir, "six_dimensions_evaluation_report.md")
        dimensions = data.get("dimensions", {})
        
        content = f"""# 六维度评估报告
        
## 总体评价
- 综合评分: {data.get('total_score', 0)}/30
- 等级: {data.get('overall_grade', 'C')}

## 各维度评分
"""
        
        for dim, info in dimensions.items():
            score = info.get("score", 0)
            stars = "★" * score + "☆" * (5 - score)
            content += f"### {dim}\n**评分**: {stars} ({score}/5)\n**评语**: {info.get('comment', '')}\n\n"
        
        with open(path, 'w', encoding='utf-8') as f:
            f.write(content)
        return path

    def _generate_radar_chart_section_standalone(self, content: str, purpose: str) -> str:
        """生成雷达图章节内容（从material_agent_refactored.py移动而来）"""
        try:
            # 使用AI代理评估内容的六个维度
            evaluation_prompt = f"""
请根据以下内容和用户目的，对项目或主题进行六维度评估，并生成雷达图JSON数据。

**用户目的**: {purpose}

**评估内容**:
{content[:5000]}  # 限制内容长度避免超长

**评估维度和标准**:
1. **核心功能完善性** (1-5分): 项目核心功能的完整性和实用性
2. **可用性与易用性** (1-5分): 用户体验和上手难度 
3. **项目活跃度** (1-5分): 社区活跃度、更新频率、维护状况
4. **代码质量** (1-5分): 代码架构、可维护性、技术选型
5. **架构设计** (1-5分): 系统设计的合理性和扩展性
6. **文档完备性** (1-5分): 文档质量和完整性

**输出要求**:
1. 请为每个维度给出1-5分的评分
2. 计算总分(满分30分)
3. 根据总分给出等级：A(25-30), B(20-24), C(15-19), D(10-14), F(0-9)
4. 为每个维度提供简短的评分理由

请以JSON格式输出评估结果，格式如下：
```json
{{
  "dimensions": {{
    "核心功能完善性": 4,
    "可用性与易用性": 3, 
    "项目活跃度": 4,
    "代码质量": 5,
    "架构设计": 4,
    "文档完备性": 3
  }},
  "total_score": 23,
  "grade": "B",
  "explanations": {{
    "核心功能完善性": "功能较为完整，满足基本需求",
    "可用性与易用性": "上手有一定难度，需要学习成本",
    "项目活跃度": "社区活跃，更新较为频繁",
    "代码质量": "代码结构清晰，技术选型合理",
    "架构设计": "架构设计良好，具有扩展性",
    "文档完备性": "文档基本完整，但可以更详细"
  }}
}}
```

请只输出JSON，不要包含其他说明。
"""

            if self.evaluator_agent:
                from camel.messages import BaseMessage
                
                evaluation_result = (
                    self.evaluator_agent
                    .step(BaseMessage.make_user_message(role_name="User", content=evaluation_prompt))
                    .msg.content
                )

                # 提取JSON部分
                json_match = re.search(r'```json\s*(\{.*?\})\s*```', evaluation_result, re.DOTALL)
                if not json_match:
                    # 如果没有代码块，尝试直接解析整个响应
                    json_match = re.search(r'(\{.*\})', evaluation_result, re.DOTALL)
                
                if json_match:
                    eval_data = json.loads(json_match.group(1))
                    return self._format_radar_chart_section(eval_data)

        except Exception as e:
            logger.warning(f"生成雷达图JSON失败: {e}")
            return ""

        return ""

    def _format_radar_chart_section(self, data: Dict[str, Any]) -> str:
        """格式化雷达图章节内容，包含JSON数据和动画配置"""
        dimensions = data.get("dimensions", {})
        total_score = data.get("total_score", 0)
        grade = data.get("overall_grade", "C")
        
        # 构建雷达图数据
        radar_data = {}
        for dim, info in dimensions.items():
            radar_data[dim] = info.get("score", 0)
        
        section = f"""## 📊 六维度评估

**综合评分**: {total_score}/30 | **等级**: {grade}

### 各维度评分详情

"""
        
        # 添加各维度详细评分
        for dim, info in dimensions.items():
            score = info.get("score", 0)
            stars = "★" * score + "☆" * (5 - score)
            comment = info.get("comment", "")
            section += f"**{dim}**: {stars} ({score}/5)  \n"
            if comment:
                section += f"*{comment}*\n\n"
            else:
                section += "\n"
        
        # 添加雷达图JSON配置
        section += f"""📈 *详细雷达图和完整报告已生成*

```json
{{
  "type": "display_markdown",
  "target_region_id": "full_screen",
  "content": "**综合评分**: {total_score}/30 | **等级**: {grade}"
}}
```

```json
{{
  "type": "animate_chart",
  "params": {{
    "chart_type": "radar",
    "data": [
      {{
{', '.join([f'        "{dim}": {info.get("score", 0)}' for dim, info in dimensions.items()])}
      }}
    ],
    "title": "六维度评估 (总分:{total_score}/30)",
    "dataset_names": [
      "评分"
    ],
    "animation_style": "grow",
    "narration": "这个雷达图展示了项目在六个维度上的评分。综合评级: {grade}"
  }}
}}
```

---

"""
        return section

    def generate_intro(self, tool_result: Dict[str, Any]) -> str:
        """生成工具输出的介绍文本 - 输出完整的六维度评估内容"""
        if tool_result.get("status") != "success":
            return "❌ 评估失败"
        
        # 获取评估数据并生成完整的六维度评估内容
        evaluation_data = tool_result.get("evaluation_data")
        if not evaluation_data:
            score = tool_result.get("metadata", {}).get("total_score", 0)
            return f"✅ 六维度评估已完成 (综合评分: {score}/30)\n📈 详细雷达图和报告已生成\n"
        
        # 使用_format_radar_chart_section方法生成完整内容
        return self._format_radar_chart_section(evaluation_data) 