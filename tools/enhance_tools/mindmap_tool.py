#!/usr/bin/env python3
"""
思维导图工具 - 内容结构化组织类
基于内容自动生成思维导图，将复杂的层次结构信息可视化展示
"""

import json
import os
import re
from typing import Any, Optional

from loguru import logger

from .base_tool import EnhancementTool, ToolCategory


class MindmapTool(EnhancementTool):
    """思维导图工具 - 内容结构化组织类"""

    tool_name = "mindmap_generation"
    tool_description = "基于内容自动生成思维导图，将复杂的层次结构信息可视化展示，帮助观众理解知识体系和概念关系"
    tool_category = ToolCategory.CONTENT_ORGANIZATION

    # 适用场景描述 - 供LLM智能选择使用
    suitable_content_types = [
        "知识体系介绍",
        "概念框架说明",
        "分类体系文档",
        "学科结构介绍",
        "技术架构说明",
        "组织结构图",
        "产品功能分类",
        "理论体系梳理",
        "课程大纲内容",
        "研究领域概述",
        "系统模块介绍",
    ]
    suitable_purposes = [
        "教学讲解视频",
        "知识体系梳理",
        "概念关系展示",
        "架构介绍视频",
        "学习资料制作",
        "培训内容展示",
        "理论框架说明",
        "系统概览介绍",
    ]
    required_conditions = [
        "内容包含明确的层次结构",
        "有清晰的分类或分支关系",
        "内容长度适中(>800字符)",
        "需要LLM模型支持",
        "包含可提取的主题和子主题",
    ]

    # 不适用场景描述 - 帮助LLM做负面判断
    unsuitable_content_types = [
        "时间序列文档",
        "流程步骤说明",
        "线性教程内容",
        "故事叙述文本",
        "代码实现细节",
        "API文档",
        "安装指南",
        "故障排除手册",
        "日志记录",
    ]
    unsuitable_purposes = [
        "时间线展示",
        "流程演示视频",
        "步骤指导教程",
        "故事讲述视频",
        "代码演示",
        "操作指南制作",
        "问题解决说明",
        "线性过程展示",
    ]
    blocking_conditions = [
        "内容过短(<800字符)",
        "无明确层次结构",
        "纯线性流程内容",
        "无LLM模型支持",
        "主要是时间序列信息",
        "缺乏分类关系",
    ]

    def __init__(self, config=None):
        self.config = config
        self.agent = None
        if config:
            self._init_model()

    def _init_model(self):
        """初始化Camel模型"""
        try:
            from camel.agents import ChatAgent
            from camel.messages import BaseMessage
            from camel.models import ModelFactory
            from camel.types import ModelPlatformType

            model_config = self.config.get("model", {})
            self.model = ModelFactory.create(
                model_platform=ModelPlatformType["OPENAI_COMPATIBLE_MODEL"],
                model_type=model_config.get("type", "openai/gpt-4o-mini"),
                api_key=model_config.get("api", {}).get("openai_compatibility_api_key"),
                url=model_config.get("api", {}).get("openai_compatibility_api_base_url"),
            )

            # 定制化系统提示词
            system_prompt = """你是专业的知识结构分析专家，负责从内容中提取层次化的思维导图结构。
你的任务是：
1. 分析内容是否适合生成思维导图（需要有明确的层次结构和分类关系）
2. 提取主题、子主题和层次关系
3. 生成符合思维导图格式的结构化数据
4. 确保提取的结构清晰、逻辑性强、层次分明

请严格按照要求的JSON格式输出，确保数据的准确性和完整性。"""

            self.agent = ChatAgent(
                system_message=BaseMessage.make_assistant_message("", system_prompt),
                model=self.model,
            )
            logger.info(f"{self.tool_name}模型初始化成功")
        except Exception as e:
            logger.warning(f"{self.tool_name}模型初始化失败: {e}")

    def get_tool_info(self) -> dict[str, Any]:
        """获取工具信息 - 供智能选择器决策使用"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "category": self.tool_category.value,
            "suitable_content": self.suitable_content_types,
            "suitable_purposes": self.suitable_purposes,
            "required_conditions": self.required_conditions,
            "unsuitable_content": self.unsuitable_content_types,
            "unsuitable_purposes": self.unsuitable_purposes,
            "blocking_conditions": self.blocking_conditions,
            "output_type": "JSON思维导图数据",
            "typical_output": "层次化的主题结构数据",
            "use_case": "当内容包含明显的层次结构和分类关系，用户需要理解知识体系时使用。不适用于时间序列或线性流程内容。",
        }

    def can_apply(self, content: str, purpose: str, context: dict[str, Any]) -> bool:
        """简化的可用性检查 - 只检查基本配置条件"""
        # 基本检查：配置存在、内容长度、配置开关
        basic_check = (
            self.config
            and len(content) >= 800  # 思维导图需要足够的内容来提取结构
            and self.config.get("material", {}).get("material_enhance", {}).get(self.tool_name, True)
        )

        # 如果需要LLM支持，检查agent是否初始化成功
        if hasattr(self, "agent") and self.agent is None:
            return False

        return basic_check

    def apply_tool(self, content: str, output_dir: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """应用工具，执行具体的扩充操作"""
        if not self.can_apply(content, context.get("purpose", ""), context):
            return None

        logger.info(f"🧠 应用{self.tool_name}工具，准备处理内容")

        try:
            os.makedirs(output_dir, exist_ok=True)

            # 生成输出路径
            output_filename = f"{self.tool_name}_output.json"
            output_path = os.path.join(output_dir, output_filename)

            # 检查是否已存在文件
            if os.path.exists(output_path):
                logger.info(f"工具输出已存在: {output_path}")
                with open(output_path, encoding="utf-8") as f:
                    existing_data = json.load(f)
                return {
                    "tool_name": self.tool_name,
                    "type": self.tool_name,
                    "file_path": output_path,
                    "data": existing_data,
                    "status": "exists",
                }

            # 执行具体的工具逻辑
            result_data = self._process_content(content, context)

            if not result_data:
                return None

            # 保存结果数据
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

            logger.info(f"🧠 {self.tool_name}工具处理完成: {output_path}")

            return {
                "tool_name": self.tool_name,
                "type": self.tool_name,
                "file_path": output_path,
                "data": result_data,
                "status": "created",
            }

        except Exception as e:
            logger.error(f"{self.tool_name}工具处理失败: {e}")
            return None

    def _process_content(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """处理内容的核心逻辑 - 使用Camel进行数据提取"""
        if not hasattr(self, "agent") or self.agent is None:
            logger.error("Camel agent未初始化，无法处理内容")
            return None

        # 使用Camel进行数据提取
        return self._process_with_camel(content, context)

    def _process_with_camel(self, content: str, context: dict[str, Any]) -> Optional[dict[str, Any]]:
        """使用Camel进行数据提取的处理逻辑"""
        purpose = context.get("purpose", "知识体系梳理")

        # 构建结构化提示词
        prompt = f"""基于以下内容进行思维导图结构分析和提取：

**内容材料**：
{content[:8000]}{'...(内容截断)' if len(content) > 8000 else ''}

**处理目标**：{purpose}

**分析要求**：
1. **适用性判断**：
   - 分析内容是否包含明确的层次结构和分类关系
   - 判断是否适合生成思维导图（需要有主题-子主题的层次关系）
   - 如果内容主要是时间序列、线性流程或缺乏层次结构，则标记为不适用

2. **结构提取**（仅在适用时进行）：
   - 识别核心主题（根节点）
   - 提取主要分支（一级子主题）
   - 识别次级分支（二级子主题）
   - 确保层次清晰，避免过深的嵌套（建议最多3层）

3. **数据格式**：
   - 按照animate_mindmap.py要求的格式生成数据
   - 使用"标题"和"子章节"字段
   - 确保结构逻辑清晰

**输出要求**：
请严格按照以下JSON格式输出：

```json
{{
    "suitable": true/false,
    "reason": "适用性判断的原因说明",
    "mindmap_data": {{
        "标题": "核心主题名称",
        "子章节": [
            {{
                "标题": "一级子主题1",
                "子章节": [
                    {{"标题": "二级子主题1"}},
                    {{"标题": "二级子主题2"}}
                ]
            }},
            {{
                "标题": "一级子主题2",
                "子章节": [
                    {{"标题": "二级子主题3"}},
                    {{"标题": "二级子主题4"}}
                ]
            }}
        ]
    }},
    "layout_style": "balance",
    "max_depth": 3,
    "focus_sequence": ["核心主题名称", "一级子主题1", "一级子主题2"],
    "narration": "简短的思维导图介绍文本",
    "metadata": {{
        "total_nodes": "节点总数",
        "max_depth": "实际最大深度",
        "structure_quality": "结构质量评估"
    }}
}}
```"""

        try:
            # 调用Camel agent进行处理
            response = self.agent.step(prompt)
            response_content = response.msgs[0].content

            # 提取JSON
            json_match = re.search(r"```json\s*(\{.*?\})\s*```", response_content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # 尝试直接解析整个响应
                json_str = response_content.strip()

            result_data = json.loads(json_str)
            return result_data

        except Exception as e:
            logger.error(f"Camel数据提取失败: {e}")
            return None

    def generate_intro(self, tool_result: dict[str, Any]) -> str:
        """生成工具输出的介绍文本"""
        data = tool_result.get("data", {})

        if not data:
            return ""

        # 检查是否适合生成思维导图
        if not data.get("suitable", False):
            reason = data.get("reason", "内容不适合生成思维导图")
            return f"## 🧠 思维导图分析\n\n❌ {reason}\n\n"

        intro = f"## 🧠 {self.tool_description}\n\n"

        # 添加基本信息
        mindmap_data = data.get("mindmap_data", {})
        metadata = data.get("metadata", {})

        if mindmap_data:
            root_title = mindmap_data.get("标题", "知识结构")
            intro += f"**核心主题**: {root_title}\n"

            # 统计节点信息
            total_nodes = metadata.get("total_nodes", "未知")
            max_depth = metadata.get("max_depth", data.get("max_depth", 3))
            intro += f"**节点总数**: {total_nodes}\n"
            intro += f"**最大深度**: {max_depth}层\n"

            # 添加结构质量评估
            structure_quality = metadata.get("structure_quality", "")
            if structure_quality:
                intro += f"**结构质量**: {structure_quality}\n"

            intro += "\n"

        # 添加思维导图预览
        if mindmap_data and mindmap_data.get("子章节"):
            intro += "**主要分支预览**:\n"
            for i, branch in enumerate(mindmap_data["子章节"][:5], 1):  # 最多显示前5个分支
                branch_title = branch.get("标题", f"分支{i}")
                intro += f"- 🌿 {branch_title}\n"

                # 显示子分支（如果有）
                sub_branches = branch.get("子章节", [])
                if sub_branches:
                    for j, sub_branch in enumerate(sub_branches[:3], 1):  # 最多显示前3个子分支
                        sub_title = sub_branch.get("标题", f"子分支{j}")
                        intro += f"  - {sub_title}\n"
                    if len(sub_branches) > 3:
                        intro += f"  - ... 以及其他 {len(sub_branches) - 3} 个子分支\n"

            if len(mindmap_data["子章节"]) > 5:
                intro += f"- ... 以及其他 {len(mindmap_data['子章节']) - 5} 个主要分支\n"
            intro += "\n"

        # 添加使用说明
        narration = data.get("narration", "")
        if narration:
            intro += f"**导图说明**: {narration}\n\n"

        # 生成animate_mindmap的DSL配置
        intro += "### 🎬 动画配置\n\n"
        intro += "以下是生成的思维导图动画配置：\n\n"
        intro += "```json\n"

        # 构建animate_mindmap所需的配置
        mindmap_config = {
            "type": "animate_mindmap",
            "params": {
                "data_source": mindmap_data,
                "layout_style": data.get("layout_style", "balance"),
                "max_depth": data.get("max_depth", 3),
                "focus_sequence": data.get("focus_sequence", []),
                "narration": narration
                or f"让我们通过这个思维导图来了解{mindmap_data.get('标题', '知识结构')}的整体架构。",
                "id": f"mindmap_{abs(hash(str(mindmap_data))) % 10000}",
            },
        }

        intro += json.dumps(mindmap_config, ensure_ascii=False, indent=2)
        intro += "\n```\n\n"

        intro += "*💡 此思维导图将帮助观众更好地理解内容的层次结构和知识体系*\n\n"

        return intro
